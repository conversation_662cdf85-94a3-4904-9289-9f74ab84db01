{"name": "amm", "version": "1.0.0", "description": "", "dependencies": {"@reduxjs/toolkit": "^2.8.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.3.0", "@testing-library/user-event": "^14.6.0", "bootstrap": "^5.3.3", "lodash": "^4.17.21", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-blockies": "^1.4.1", "react-bootstrap": "^2.10.1", "react-dom": "^18.3.1", "react-redux": "^9.1.0", "react-router-bootstrap": "^0.26.2", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "reselect": "^5.1.0", "web-vitals": "^3.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<EMAIL>", "license": "ISC", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "hardhat": "^2.20.1"}}