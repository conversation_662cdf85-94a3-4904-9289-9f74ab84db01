// price_checker.js
require('dotenv').config();
const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const winston = require('winston');

// Configure logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ level, message, timestamp }) => {
      return `${timestamp} ${level}: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'price_checker.log' })
  ]
});

// Token addresses (Ethereum mainnet)
const TOKEN_ADDRESSES = {
  // Major cryptocurrencies
  'ETH': '******************************************',  // WETH
  'BTC': '******************************************',  // WBTC
  'WBTC': '******************************************',  // WBTC (explicit)
  
  // Stablecoins
  'DAI': '******************************************',  // DAI
  'USDC': '******************************************',  // USDC
  'USDT': '******************************************',  // USDT
  
  // DeFi protocol tokens
  'LINK': '******************************************',  // Chainlink
  'UNI': '******************************************',  // Uniswap
  'AAVE': '******************************************',  // Aave
  'SNX': '******************************************',  // Synthetix
  'YFI': '******************************************',  // Yearn Finance
  'CRV': '******************************************',  // Curve DAO
  'COMP': '******************************************',  // Compound
  'MKR': '******************************************',  // Maker
  'MATIC': '******************************************',  // Polygon (on Ethereum)
};

// DEX router addresses
const DEX_ADDRESSES = {
  'Uniswap_V2': '******************************************',  // Uniswap V2 Router
  'Uniswap_V3': '******************************************',  // Uniswap V3 Router
  'SushiSwap': '******************************************',  // SushiSwap Router
};

// Stablecoin addresses
const USDC_ADDRESS = '******************************************';
const USDT_ADDRESS = '******************************************';
const DAI_ADDRESS = '******************************************';

// Stablecoin references for more accurate pricing
const STABLECOINS = [USDC_ADDRESS, USDT_ADDRESS, DAI_ADDRESS];

// Uniswap V3 Quoter address
const UNISWAP_V3_QUOTER = '******************************************';

// Directory for ABIs
const ABI_DIR = path.join(__dirname, 'abis');

// Make sure ABI directory exists
if (!fs.existsSync(ABI_DIR)) {
  fs.mkdirSync(ABI_DIR, { recursive: true });
}

// Paths for ABI files
const ABI_FILES = {
  'ERC20': path.join(ABI_DIR, 'erc20_abi.json'),
  'UniswapV2Router': path.join(ABI_DIR, 'uniswap_v2_router_abi.json'),
  'UniswapV3Quoter': path.join(ABI_DIR, 'uniswap_v3_quoter_abi.json'),
  'SushiSwapRouter': path.join(ABI_DIR, 'sushiswap_router_abi.json'),
};

// Minimal ABIs for fallback
const MINIMAL_ABIS = {
  'ERC20': [
    {
      "constant": true,
      "inputs": [],
      "name": "decimals",
      "outputs": [{"name": "", "type": "uint8"}],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [{"name": "owner", "type": "address"}],
      "name": "balanceOf",
      "outputs": [{"name": "", "type": "uint256"}],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "symbol",
      "outputs": [{"name": "", "type": "string"}],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    }
  ],
  'UniswapV2Router': [
    {
      "inputs": [
        {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
        {"internalType": "address[]", "name": "path", "type": "address[]"}
      ],
      "name": "getAmountsOut",
      "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
      "stateMutability": "view",
      "type": "function"
    }
  ],
  'UniswapV3Quoter': [
    {
      "inputs": [
        {"internalType": "address", "name": "tokenIn", "type": "address"},
        {"internalType": "address", "name": "tokenOut", "type": "address"},
        {"internalType": "uint24", "name": "fee", "type": "uint24"},
        {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
        {"internalType": "uint160", "name": "sqrtPriceLimitX96", "type": "uint160"}
      ],
      "name": "quoteExactInputSingle",
      "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}],
      "stateMutability": "nonpayable",
      "type": "function"
    }
  ]
};

// Initialize provider
let provider;
let shutdownRequested = false;

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('Shutdown signal received. Completing current cycle...');
  shutdownRequested = true;
});

// Load ABI from file or use minimal ABI as fallback
function loadAbi(abiType) {
  const filePath = ABI_FILES[abiType];
  try {
    if (fs.existsSync(filePath)) {
      const abiData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      logger.info(`Loaded ${abiType} ABI from file with ${abiData.length} functions`);
      return abiData;
    } else {
      // Use minimal ABI
      logger.info(`Using minimal ${abiType} ABI as fallback`);
      fs.writeFileSync(filePath, JSON.stringify(MINIMAL_ABIS[abiType], null, 2));
      return MINIMAL_ABIS[abiType];
    }
  } catch (error) {
    logger.error(`Error loading ${abiType} ABI: ${error.message}`);
    return MINIMAL_ABIS[abiType];
  }
}

// Initialize ABIs
const ABIS = {
  'ERC20': loadAbi('ERC20'),
  'UniswapV2Router': loadAbi('UniswapV2Router'),
  'UniswapV3Quoter': loadAbi('UniswapV3Quoter'),
  'SushiSwapRouter': loadAbi('UniswapV2Router'), // SushiSwap is compatible with Uniswap V2
};

// Initialize provider and check connection
async function initializeProvider() {
  try {
    const infuraKey = process.env.INFURA_API_KEY;
    
    if (!infuraKey) {
      logger.error('INFURA_API_KEY not found in .env file');
      logger.info('Creating a new .env file with placeholder for API key...');
      
      fs.writeFileSync('.env', '# Get your API key from https://infura.io\nINFURA_API_KEY=YOUR_API_KEY_HERE\n');
      
      process.exit(1);
    }
    
    const infuraUrl = `https://mainnet.infura.io/v3/${infuraKey}`;
    provider = new ethers.providers.JsonRpcProvider(infuraUrl);
    
    // Check connection
    const network = await provider.getNetwork();
    logger.info(`Successfully connected to Ethereum network: ${network.name} (chainId: ${network.chainId})`);
    
    return true;
  } catch (error) {
    logger.error(`Failed to initialize provider: ${error.message}`);
    return false;
  }
}

// Fetch reference prices from CoinGecko
async function getCoinGeckoPrices() {
  try {
    logger.info('Fetching reference prices from CoinGecko API...');
    
    const response = await axios.get(
      'https://api.coingecko.com/api/v3/simple/price?ids=ethereum,bitcoin,wrapped-bitcoin,dai,usd-coin,tether,chainlink,uniswap,aave,synthetix-network-token,yearn-finance,curve-dao-token,compound-governance-token,maker,matic-network&vs_currencies=usd',
      {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DEX-Price-Monitor/1.0'
        }
      }
    );
    
    // Map CoinGecko IDs to our token symbols
    const idToSymbol = {
      'ethereum': 'ETH',
      'bitcoin': 'BTC',
      'wrapped-bitcoin': 'WBTC',
      'dai': 'DAI',
      'usd-coin': 'USDC',
      'tether': 'USDT',
      'chainlink': 'LINK',
      'uniswap': 'UNI',
      'aave': 'AAVE',
      'synthetix-network-token': 'SNX',
      'yearn-finance': 'YFI',
      'curve-dao-token': 'CRV',
      'compound-governance-token': 'COMP',
      'maker': 'MKR',
      'matic-network': 'MATIC'
    };
    
    const data = response.data;
    const referencePrices = {};
    
    for (const [geckoId, symbol] of Object.entries(idToSymbol)) {
      if (data[geckoId] && data[geckoId].usd) {
        referencePrices[symbol] = data[geckoId].usd;
      }
    }
    
    logger.info(`Successfully fetched ${Object.keys(referencePrices).length} reference prices from CoinGecko`);
    return referencePrices;
  } catch (error) {
    logger.error(`Error fetching reference prices from CoinGecko: ${error.message}`);
    
    // Use hardcoded fallback prices (updated March 2025)
    return {
      'ETH': 2520,
      'BTC': 93500,
      'WBTC': 93400,
      'DAI': 1,
      'USDC': 1,
      'USDT': 1,
      'LINK': 15,
      'UNI': 7,
      'AAVE': 90,
      'SNX': 3,
      'YFI': 8000,
      'CRV': 0.5,
      'COMP': 55,
      'MKR': 1800,
      'MATIC': 0.8
    };
  }
}

// Get price from Uniswap V2 style DEXes
async function getUniswapV2Price(tokenSymbol, dexName, referencePrices) {
  try {
    const tokenAddress = TOKEN_ADDRESSES[tokenSymbol];
    const dexAddress = DEX_ADDRESSES[dexName];
    
    // Get router ABI based on DEX
    const routerAbi = (dexName === 'SushiSwap') ? ABIS.SushiSwapRouter : ABIS.UniswapV2Router;
    
    // Initialize contracts
    const routerContract = new ethers.Contract(dexAddress, routerAbi, provider);
    const tokenContract = new ethers.Contract(tokenAddress, ABIS.ERC20, provider);
    
    // Get token decimals
    let tokenDecimals;
    try {
      tokenDecimals = await tokenContract.decimals();
      tokenDecimals = parseInt(tokenDecimals.toString());
    } catch (error) {
      logger.warn(`Error getting decimals for ${tokenSymbol}: ${error.message}`);
      tokenDecimals = 18; // Default to 18 if we can't get decimals
    }
    
    // Check price against multiple stablecoins for better accuracy
    const prices = [];
    let successCount = 0;
    
    // Try different input amounts to avoid price impact issues
    const amountScales = [1, 0.1, 0.01];
    
    for (const stablecoin of STABLECOINS) {
      for (const scaleFactor of amountScales) {
        try {
          // Get path: Token -> Stablecoin
          const pricePath = [tokenAddress, stablecoin];
          
          // Scaled input amount
          const amountIn = ethers.BigNumber.from(10).pow(tokenDecimals).mul(
            Math.floor(scaleFactor * 100)
          ).div(100);
          
          if (amountIn.eq(0)) continue;
          
          // Get amounts out
          const amountsOut = await routerContract.getAmountsOut(amountIn, pricePath);
          
          // Get stablecoin decimals
          const stableContract = new ethers.Contract(stablecoin, ABIS.ERC20, provider);
          
          let stableDecimals;
          try {
            stableDecimals = await stableContract.decimals();
            stableDecimals = parseInt(stableDecimals.toString());
          } catch (error) {
            // Default USDC/USDT to 6, DAI to 18
            if (stablecoin === USDC_ADDRESS || stablecoin === USDT_ADDRESS) {
              stableDecimals = 6;
            } else {
              stableDecimals = 18;
            }
          }
          
          // Calculate price
          const amountOut = amountsOut[1];
          const divisor = ethers.BigNumber.from(10).pow(stableDecimals);
          const price = parseFloat(ethers.utils.formatUnits(amountOut, stableDecimals)) / scaleFactor;
          
          // Skip any clearly wrong prices by comparing against reference prices
          if (tokenSymbol in referencePrices) {
            const refPrice = referencePrices[tokenSymbol];
            if (price < refPrice * 0.5 || price > refPrice * 2) {
              logger.warn(`Skipping outlier price for ${tokenSymbol} on ${dexName} via ${stablecoin.slice(-4)}: $${price.toFixed(2)} (reference: $${refPrice.toFixed(2)})`);
              continue;
            }
          }
          
          prices.push(price);
          successCount++;
          logger.info(`[${dexName}] ${tokenSymbol} -> ${stablecoin.slice(-4)}: $${price.toFixed(2)}`);
          
          // If we got a good price, we can break the inner loop
          break;
        } catch (error) {
          if (scaleFactor === 1) { // Only log the first attempt for cleaner output
            logger.debug(`Failed to get price on ${dexName} from ${tokenSymbol} to ${stablecoin.slice(-4)} with scale ${scaleFactor}: ${error.message}`);
          }
          continue;
        }
      }
    }
    
    // Return median price if we have multiple values
    if (prices.length > 0) {
      prices.sort((a, b) => a - b);
      
      // For 3 or more prices, remove highest and lowest
      if (prices.length >= 3) {
        prices.splice(0, 1); // Remove lowest
        prices.splice(prices.length - 1, 1); // Remove highest
      }
      
      const medianPrice = prices.length % 2 === 0
        ? (prices[prices.length / 2 - 1] + prices[prices.length / 2]) / 2
        : prices[Math.floor(prices.length / 2)];
      
      return {
        source: dexName,
        price: medianPrice,
        paths_checked: successCount,
        timestamp: new Date().toISOString()
      };
    } else {
      logger.warn(`No valid price paths found for ${tokenSymbol} on ${dexName}`);
      return null;
    }
  } catch (error) {
    logger.error(`Error getting ${dexName} price for ${tokenSymbol}: ${error.message}`);
    return null;
  }
}

// Get price from Uniswap V3
async function getUniswapV3Price(tokenSymbol, referencePrices) {
  try {
    const tokenAddress = TOKEN_ADDRESSES[tokenSymbol];
    
    // Initialize contracts
    const quoterContract = new ethers.Contract(UNISWAP_V3_QUOTER, ABIS.UniswapV3Quoter, provider);
    const tokenContract = new ethers.Contract(tokenAddress, ABIS.ERC20, provider);
    
    // Get token decimals
    let tokenDecimals;
    try {
      tokenDecimals = await tokenContract.decimals();
      tokenDecimals = parseInt(tokenDecimals.toString());
    } catch (error) {
      logger.warn(`Error getting decimals for ${tokenSymbol}: ${error.message}`);
      tokenDecimals = 18; // Default to 18 if we can't get decimals
    }
    
    // Price check against different stablecoins and fee tiers
    const feeTiers = [500, 3000, 10000]; // 0.05%, 0.3%, 1% fee tiers
    
    const bestPrices = [];
    let successCount = 0;
    
    // Try different input amounts to avoid price impact issues
    const amountScales = [1, 0.1, 0.01, 0.001];
    
    for (const stablecoin of STABLECOINS) {
      const stableContract = new ethers.Contract(stablecoin, ABIS.ERC20, provider);
      
      let stableDecimals;
      try {
        stableDecimals = await stableContract.decimals();
        stableDecimals = parseInt(stableDecimals.toString());
      } catch (error) {
        // Default USDC/USDT to 6, DAI to 18
        if (stablecoin === USDC_ADDRESS || stablecoin === USDT_ADDRESS) {
          stableDecimals = 6;
        } else {
          stableDecimals = 18;
        }
      }
      
      for (const fee of feeTiers) {
        for (const scaleFactor of amountScales) {
          try {
            // Amount to query with scaling
            const amountIn = ethers.BigNumber.from(10).pow(tokenDecimals).mul(
              Math.floor(scaleFactor * 100)
            ).div(100);
            
            if (amountIn.eq(0)) continue;
            
            // Call quoteExactInputSingle for more accurate pricing
            const quote = await quoterContract.callStatic.quoteExactInputSingle(
              tokenAddress,
              stablecoin,
              fee,
              amountIn,
              0 // 0 sqrtPriceLimitX96 means no price limit
            );
            
            // Calculate scaled price
            const price = parseFloat(ethers.utils.formatUnits(quote, stableDecimals)) / scaleFactor;
            
            // Skip any clearly wrong prices by comparing against reference
            if (tokenSymbol in referencePrices) {
              const refPrice = referencePrices[tokenSymbol];
              if (price < refPrice * 0.5 || price > refPrice * 2) {
                logger.warn(`Skipping outlier price for ${tokenSymbol} on UniV3 via ${stablecoin.slice(-4)} (fee ${fee/10000}%): $${price.toFixed(2)} (reference: $${refPrice.toFixed(2)})`);
                continue;
              }
            }
            
            bestPrices.push(price);
            successCount++;
            logger.info(`[UniV3] ${tokenSymbol} -> ${stablecoin.slice(-4)} (fee ${fee/10000}%): $${price.toFixed(2)}`);
            
            // If we got a good price, break the inner loop
            break;
          } catch (error) {
            // Skip this fee tier if it doesn't exist
            if (scaleFactor === 1) { // Only log the first attempt for cleaner output
              logger.debug(`Fee tier ${fee/10000}% for ${tokenSymbol}->${stablecoin.slice(-4)} not available: ${error.message}`);
            }
            continue;
          }
        }
      }
    }
    
    if (bestPrices.length > 0) {
      // Filter out extreme outliers (more than 2x from median)
      if (bestPrices.length >= 3) {
        bestPrices.sort((a, b) => a - b);
        const median = bestPrices[Math.floor(bestPrices.length / 2)];
        const filteredPrices = bestPrices.filter(p => p >= median * 0.5 && p <= median * 2);
        
        // Only use filtered if we didn't filter too much
        if (filteredPrices.length >= bestPrices.length * 0.5) {
          bestPrices.length = 0;
          bestPrices.push(...filteredPrices);
        }
      }
      
      // Sort and get median price
      bestPrices.sort((a, b) => a - b);
      const medianPrice = bestPrices.length % 2 === 0
        ? (bestPrices[bestPrices.length / 2 - 1] + bestPrices[bestPrices.length / 2]) / 2
        : bestPrices[Math.floor(bestPrices.length / 2)];
      
      return {
        source: "Uniswap_V3",
        price: medianPrice,
        paths_checked: successCount,
        timestamp: new Date().toISOString()
      };
    } else {
      logger.warn(`No valid price paths found for ${tokenSymbol} on Uniswap V3`);
      return null;
    }
  } catch (error) {
    logger.error(`Error getting Uniswap V3 price for ${tokenSymbol}: ${error.message}`);
    return null;
  }
}

// Aggregate prices from multiple DEXes with improved outlier detection
function aggregatePrices(pricesData, tokenSymbol, referencePrices) {
  if (!pricesData || pricesData.length === 0) {
    return null;
  }
  
  // Remove null values
  const validPrices = pricesData.filter(p => p !== null);
  
  if (validPrices.length === 0) {
    logger.warn(`No valid prices for ${tokenSymbol}`);
    return null;
  }
  
  // Extract price values for analysis
  const priceValues = validPrices.map(p => p.price);
  
  // If we have a reference price, use it for sanity checking
  let refPrice = null;
  if (tokenSymbol in referencePrices) {
    refPrice = referencePrices[tokenSymbol];
    logger.info(`Reference price for ${tokenSymbol}: $${refPrice.toFixed(2)}`);
    
    // Filter out extreme outliers using reference price
    let validPricesRef = [];
    for (const priceData of validPrices) {
      const price = priceData.price;
      const deviation = Math.abs(price - refPrice) / refPrice;
      
      if (deviation > 0.5) { // More than 50% deviation from reference
        logger.warn(`⚠️ Detected extreme outlier: ${priceData.source} reported $${price.toFixed(2)} (ref: $${refPrice.toFixed(2)}, deviation: ${(deviation*100).toFixed(1)}%)`);
        // Skip this price if it's way off
        if (deviation > 0.8) { // More than 80% deviation
          continue;
        }
      }
      
      validPricesRef.push(priceData);
    }
    
    // Only use filtered prices if we have enough left
    if (validPricesRef.length >= 1) {
      validPrices.length = 0;
      validPrices.push(...validPricesRef);
    }
  }
  
  if (validPrices.length === 0) {
    logger.warn(`No valid prices left for ${tokenSymbol} after filtering`);
    return refPrice; // Return reference price as fallback
  }
  
  // Assign weights (prioritize V3 and higher liquidity sources)
  const weights = {
    "Uniswap_V3": 2.0,
    "Uniswap_V2": 1.5,
    "SushiSwap": 1.2
  };
  
  // Calculate weighted average
  let totalWeight = 0;
  let weightedSum = 0;
  
  for (const priceData of validPrices) {
    const source = priceData.source;
    const price = priceData.price;
    let weight = weights[source] || 1.0;
    
    // Adjust weight by number of paths checked (more liquidity)
    if (priceData.paths_checked) {
      weight *= Math.min(priceData.paths_checked / 2, 2); // Cap at doubling the weight
    }
    
    weightedSum += price * weight;
    totalWeight += weight;
  }
  
  if (totalWeight === 0) {
    // If weighting fails, return median
    priceValues.sort((a, b) => a - b);
    return priceValues[Math.floor(priceValues.length / 2)];
  }
  
  // Return weighted average of filtered prices
  return weightedSum / totalWeight;
}

// Get all DEX prices for a token
async function getTokenDexPrices(tokenSymbol, referencePrices) {
  logger.info(`\nFetching prices for ${tokenSymbol}...`);
  
  try {
    // Get prices from all DEXes in parallel
    const [uniswapV2Price, sushiswapPrice, uniswapV3Price] = await Promise.all([
      getUniswapV2Price(tokenSymbol, "Uniswap_V2", referencePrices),
      getUniswapV2Price(tokenSymbol, "SushiSwap", referencePrices),
      getUniswapV3Price(tokenSymbol, referencePrices)
    ]);
    
    const allPrices = [uniswapV2Price, sushiswapPrice, uniswapV3Price];
    
    // Filter out failed queries
    const validPrices = allPrices.filter(p => p !== null);
    
    if (validPrices.length === 0) {
      logger.warn(`Warning: No valid prices found for ${tokenSymbol}`);
      
      // Use reference price as fallback
      if (tokenSymbol in referencePrices) {
        const refPrice = referencePrices[tokenSymbol];
        logger.info(`Using reference price for ${tokenSymbol}: $${refPrice.toFixed(2)}`);
        
        return {
          sources: [],
          reference_price: refPrice,
          price: refPrice,
          timestamp: new Date().toISOString()
        };
      }
      
      return {
        sources: [],
        price: null,
        timestamp: new Date().toISOString()
      };
    }
    
    // Aggregate prices
    const aggregatedPrice = aggregatePrices(validPrices, tokenSymbol, referencePrices);
    
    // Print summary
    if (aggregatedPrice) {
      logger.info(`✅ ${tokenSymbol} aggregated price: $${aggregatedPrice.toFixed(2)}`);
      logger.info(`   Sources: ${validPrices.length}`);
      for (const source of validPrices) {
        logger.info(`   - ${source.source}: $${source.price.toFixed(2)}`);
      }
    } else {
      logger.error(`❌ Could not determine ${tokenSymbol} price`);
    }
    
    return {
      sources: validPrices,
      price: aggregatedPrice,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error(`Error getting prices for ${tokenSymbol}: ${error.message}`);
    return {
      sources: [],
      price: null,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// Get prices for all tokens
async function getAllTokenPrices() {
  const tokenSymbols = Object.keys(TOKEN_ADDRESSES);
  const results = {};
  
  // Get reference prices first (to use for validation)
  const referencePrices = await getCoinGeckoPrices();
  
  // Process tokens in batches to avoid rate limiting and timeout issues
  const batchSize = 3;
  for (let i = 0; i < tokenSymbols.length; i += batchSize) {
    const batch = tokenSymbols.slice(i, i + batchSize);
    
    // Process batch in parallel
    const batchPromises = batch.map(symbol => getTokenDexPrices(symbol, referencePrices));
    const batchResults = await Promise.all(batchPromises);
    
    // Add batch results to the overall results
    batch.forEach((symbol, index) => {
      results[symbol] = batchResults[index];
    });
    
    // Add a small delay between batches to avoid overloading the provider
    if (i + batchSize < tokenSymbols.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

// Check a single token (useful for testing)
async function checkSingleToken(tokenSymbol) {
  if (!Object.keys(TOKEN_ADDRESSES).includes(tokenSymbol)) {
    logger.error(`Token ${tokenSymbol} not found in TOKEN_ADDRESSES dictionary`);
    logger.info(`Available tokens: ${Object.keys(TOKEN_ADDRESSES).join(', ')}`);
    return;
  }
  
  const referencePrices = await getCoinGeckoPrices();
  const result = await getTokenDexPrices(tokenSymbol, referencePrices);
  
  // Pretty print the result
  logger.info('\nFinal result:');
  if (result.price !== null) {
    logger.info(`${tokenSymbol} price: ${result.price.toFixed(2)}`);
  } else {
    logger.info(`${tokenSymbol} price: Unable to determine`);
  }
  
  logger.info(`Timestamp: ${result.timestamp}`);
  
  // Save to a single token JSON file
  const output = {
    token: tokenSymbol,
    price: result,
    updated_at: new Date().toISOString()
  };
  
  const filename = `${tokenSymbol.toLowerCase()}_price.json`;
  fs.writeFileSync(filename, JSON.stringify(output, null, 2));
  
  logger.info(`Results saved to ${filename}`);
  
  return result;
}

// Monitor prices at regular intervals
async function monitorPrices(intervalSeconds = 60) {
  logger.info('Starting DEX price monitoring service...');
  logger.info(`Checking prices every ${intervalSeconds} seconds`);
  logger.info(`Monitoring tokens: ${Object.keys(TOKEN_ADDRESSES).join(', ')}`);
  logger.info('-'.repeat(50));
  
  while (!shutdownRequested) {
    try {
      const startTime = Date.now();
      
      // Get all prices
      const prices = await getAllTokenPrices();
      
      // Format output
      const output = {
        prices: prices,
        updated_at: new Date().toISOString()
      };
      
      // Save to JSON
      fs.writeFileSync('token_prices.json', JSON.stringify(output, null, 2));
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      logger.info(`\nPrices updated at ${output.updated_at}`);
      logger.info(`Update took ${duration.toFixed(2)} seconds`);
      logger.info('-'.repeat(50));
      
      // Calculate wait time (ensuring we don't have negative wait times if processing took longer)
      const waitTime = Math.max(0.1, intervalSeconds - duration);
      logger.info(`Next update in ${waitTime.toFixed(1)} seconds...`);
      
      // Wait for next interval
      if (!shutdownRequested) {
        await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
      }
    } catch (error) {
      logger.error(`❌ Error in monitoring loop: ${error.message}`);
      
      // Wait a bit before retrying
      if (!shutdownRequested) {
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
  }
  
  logger.info('Monitoring service shutting down gracefully...');
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  let tokenToCheck = null;
  let interval = 60;
  
  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--token' && i + 1 < args.length) {
      tokenToCheck = args[i + 1].toUpperCase();
      i++;
    } else if (args[i] === '--interval' && i + 1 < args.length) {
      interval = parseInt(args[i + 1]);
      if (isNaN(interval) || interval < 1) {
        interval = 60;
      }
      i++;
    }
  }
  
  // Initialize provider connection
  const connected = await initializeProvider();
  if (!connected) {
    process.exit(1);
  }
  
  if (tokenToCheck) {
    // Check single token price
    await checkSingleToken(tokenToCheck);
    process.exit(0);
  } else {
    // Run the monitoring service
    await monitorPrices(interval);
    process.exit(0);
  }
}

// Run the main function
main().catch(error => {
  logger.error(`Fatal error: ${error.message}`);
  logger.error(error.stack);
  process.exit(1);
});