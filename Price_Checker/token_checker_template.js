// simple_price_checker.js

// Load configuration from .env file or custom path specified in DOTENV_CONFIG_PATH
require('dotenv').config({
  path: process.env.DOTENV_CONFIG_PATH || '.env'
});
const { ethers } = require('ethers');
const winston = require('winston');
const fs = require('fs').promises;
const path = require('path');

// Configure logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ level, message, timestamp }) => {
      return `${timestamp} ${level}: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'price_check.log' })
  ]
});

// Token configuration from .env file
const TOKEN_CONFIG = {
  symbol: process.env.TOKEN_SYMBOL || 'LINK',
  address: process.env.TOKEN_ADDRESS || '******************************************',
  decimals: parseInt(process.env.TOKEN_DECIMALS || '18')
};

// Validate token configuration
if (!TOKEN_CONFIG.symbol || !TOKEN_CONFIG.address) {
  logger.error('TOKEN_SYMBOL and TOKEN_ADDRESS must be defined in .env file');
  process.exit(1);
}

// Constants
const OUTPUT_FILE = process.env.OUTPUT_FILE || 'token_prices.json';
const MAX_RETRIES = 5;
const RETRY_DELAY = 1000; // 1 second

// DEX Addresses from .env file (with defaults)
const DEX_ADDRESSES = {
  'Uniswap_V2': process.env.UNISWAP_V2_ROUTER || '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D',
  'Uniswap_V3': process.env.UNISWAP_V3_ROUTER || '0xE592427A0AEce92De3Edee1F18E0157C05861564',
  'SushiSwap': process.env.SUSHISWAP_ROUTER || '0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F'
};

// Uniswap V3 Quoter from .env file (with default)
const UNISWAP_V3_QUOTER = process.env.UNISWAP_V3_QUOTER || '0xb27308f9F90D607463bb33eA1BeBb41C27CE5AB6';

// Stablecoins with decimals from .env file (with defaults)
let STABLECOINS = [
  { address: '0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48', decimals: 6 }, // USDC
  { address: '0xdAC17F958D2ee523a2206206994597C13D831ec7', decimals: 6 }, // USDT
  { address: '0x6B175474E89094C44Da98b954EedeAC495271d0F', decimals: 18 } // DAI
];

// Parse custom stablecoins from .env if provided
if (process.env.STABLECOIN_ADDRESSES && process.env.STABLECOIN_DECIMALS) {
  try {
    const addresses = process.env.STABLECOIN_ADDRESSES.split(',').map(addr => addr.trim());
    const decimals = process.env.STABLECOIN_DECIMALS.split(',').map(dec => parseInt(dec.trim()));
    
    if (addresses.length === decimals.length) {
      STABLECOINS = addresses.map((address, i) => ({
        address,
        decimals: decimals[i]
      }));
      logger.info(`Loaded ${STABLECOINS.length} stablecoins from .env file`);
    } else {
      logger.warn('Mismatched stablecoin addresses and decimals in .env file, using defaults');
    }
  } catch (error) {
    logger.warn(`Error parsing stablecoin data from .env: ${error.message}. Using defaults.`);
  }
}

// Minimal ABI for price checking
const MINIMAL_ABI = [
  'function decimals() view returns (uint8)',
  'function getAmountsOut(uint amountIn, address[] memory path) view returns (uint[] memory amounts)',
  'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)'
];

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function waitForFileLock(filePath, retries = 0) {
  try {
    try {
      await fs.access(filePath, fs.constants.R_OK | fs.constants.W_OK);
      const stats = await fs.stat(filePath);
      const now = Date.now();
      
      if (now - stats.mtimeMs < 1000) {
        if (retries >= MAX_RETRIES) {
          throw new Error('Max retries reached waiting for file lock');
        }
        logger.info(`File is locked, waiting ${RETRY_DELAY}ms...`);
        await sleep(RETRY_DELAY);
        return waitForFileLock(filePath, retries + 1);
      }
    } catch (error) {
      if (error.code !== 'ENOENT') throw error;
      logger.info('Output file does not exist, will create new file');
    }
    return true;
  } catch (error) {
    logger.error(`Error in waitForFileLock: ${error.message}`);
    throw error;
  }
}

async function saveToJson(data) {
  // Ensure output file has .json extension
  const outputFile = OUTPUT_FILE.endsWith('.json') ? OUTPUT_FILE : `${OUTPUT_FILE}.json`;
  const filePath = path.join(process.cwd(), outputFile);
  
  logger.info(`Output will be saved to: ${filePath}`);
  
  try {
    logger.info(`Attempting to save data to ${filePath}`);
    await waitForFileLock(filePath);
    
    let existingData = {};
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      if (fileContent && fileContent.trim()) {
        existingData = JSON.parse(fileContent);
      }
    } catch (error) {
      if (error.code !== 'ENOENT') {
        logger.warn(`Error reading file: ${error.message}`);
      }
      logger.info('No existing file found, creating new one');
    }

    existingData[data.symbol] = {
      ...data,
      timestamp: new Date().toISOString()
    };

    await fs.writeFile(filePath, JSON.stringify(existingData, null, 2), 'utf8');
    logger.info('Successfully wrote data to file');
    
    const verifyContent = await fs.readFile(filePath, 'utf8');
    const verifyData = JSON.parse(verifyContent);
    if (verifyData[data.symbol]) {
      logger.info('Verified data was saved correctly');
    }
    
    return true;
  } catch (error) {
    logger.error(`Error saving to JSON: ${error.message}`);
    throw error;
  }
}

async function initializeProvider() {
  const infuraKey = process.env.INFURA_API_KEY;
  if (!infuraKey) {
    logger.error('INFURA_API_KEY not found in .env file');
    process.exit(1);
  }
  return new ethers.providers.JsonRpcProvider(`https://mainnet.infura.io/v3/${infuraKey}`);
}

async function checkUniswapV2Price(provider, dexAddress, stablecoin) {
  try {
    const router = new ethers.Contract(dexAddress, MINIMAL_ABI, provider);
    const amountIn = ethers.utils.parseUnits('1', TOKEN_CONFIG.decimals);
    const path = [TOKEN_CONFIG.address, stablecoin.address];
    
    const amounts = await router.getAmountsOut(amountIn, path);
    const price = ethers.utils.formatUnits(amounts[1], stablecoin.decimals);
    return parseFloat(price);
  } catch (error) {
    logger.debug(`V2 price check failed: ${error.message}`);
    return null;
  }
}

async function checkUniswapV3Price(provider, stablecoin) {
  try {
    const quoter = new ethers.Contract(UNISWAP_V3_QUOTER, MINIMAL_ABI, provider);
    const amountIn = ethers.utils.parseUnits('1', TOKEN_CONFIG.decimals);
    
    for (const fee of [500, 3000, 10000]) {
      try {
        const amountOut = await quoter.callStatic.quoteExactInputSingle(
          TOKEN_CONFIG.address,
          stablecoin.address,
          fee,
          amountIn,
          0
        );
        return parseFloat(ethers.utils.formatUnits(amountOut, stablecoin.decimals));
      } catch (error) {
        continue;
      }
    }
    return null;
  } catch (error) {
    logger.debug(`V3 price check failed: ${error.message}`);
    return null;
  }
}

async function getPrices() {
  const provider = await initializeProvider();
  const pricesByDex = {
    'Uniswap V2': [],
    'Uniswap V3': [],
    'SushiSwap': []
  };
  
  // Log configuration
  logger.info('=== Price Checker Configuration ===');
  logger.info(`Token: ${TOKEN_CONFIG.symbol} (${TOKEN_CONFIG.address})`);
  logger.info(`Decimals: ${TOKEN_CONFIG.decimals}`);
  logger.info(`Output file: ${OUTPUT_FILE}`);
  logger.info(`Stablecoins: ${STABLECOINS.length}`);
  logger.info('===============================');
  
  logger.info(`Checking prices for ${TOKEN_CONFIG.symbol}...`);

  const allPrices = {
    symbol: TOKEN_CONFIG.symbol,
    address: TOKEN_CONFIG.address,
    rawPrices: {},
    filteredPrices: {},
    medianPrice: null,
    pairs: {}
  };

  // Collect prices by DEX
  for (const stablecoin of STABLECOINS) {
    const uniV2Price = await checkUniswapV2Price(provider, DEX_ADDRESSES.Uniswap_V2, stablecoin);
    if (uniV2Price && uniV2Price < 1000) {
      const pairInfo = {
        price: uniV2Price,
        stablecoin: stablecoin.address,
        pair: `${TOKEN_CONFIG.symbol}/${stablecoin.address.slice(0, 6)}`
      };
      pricesByDex['Uniswap V2'].push(pairInfo);
      allPrices.pairs[`UniV2_${pairInfo.pair}`] = pairInfo;
    }

    const sushiPrice = await checkUniswapV2Price(provider, DEX_ADDRESSES.SushiSwap, stablecoin);
    if (sushiPrice && sushiPrice < 1000) {
      const pairInfo = {
        price: sushiPrice,
        stablecoin: stablecoin.address,
        pair: `${TOKEN_CONFIG.symbol}/${stablecoin.address.slice(0, 6)}`
      };
      pricesByDex['SushiSwap'].push(pairInfo);
      allPrices.pairs[`Sushi_${pairInfo.pair}`] = pairInfo;
    }

    const uniV3Price = await checkUniswapV3Price(provider, stablecoin);
    if (uniV3Price && uniV3Price < 1000) {
      const pairInfo = {
        price: uniV3Price,
        stablecoin: stablecoin.address,
        pair: `${TOKEN_CONFIG.symbol}/${stablecoin.address.slice(0, 6)}`
      };
      pricesByDex['Uniswap V3'].push(pairInfo);
      allPrices.pairs[`UniV3_${pairInfo.pair}`] = pairInfo;
    }
  }

  // Process prices
  logger.info('\nRaw prices by DEX:');
  const bestPrices = [];
  for (const [dex, prices] of Object.entries(pricesByDex)) {
    if (prices.length > 0) {
      allPrices.rawPrices[dex] = prices.map(p => ({
        price: p.price,
        pair: p.pair
      }));
      
      prices.forEach(p => 
        logger.info(`${dex} (${p.pair}): $${p.price.toFixed(4)}`)
      );
      
      prices.sort((a, b) => a.price - b.price);
      const medianPrice = prices[Math.floor(prices.length / 2)];
      bestPrices.push({
        source: dex,
        ...medianPrice
      });
    }
  }

  if (bestPrices.length > 0) {
    const avg = bestPrices.reduce((sum, p) => sum + p.price, 0) / bestPrices.length;
    const validPrices = bestPrices.filter(p => 
      Math.abs(p.price - avg) / avg < 0.3
    );

    if (validPrices.length > 0) {
      allPrices.filteredPrices = validPrices.reduce((acc, p) => {
        acc[p.source] = {
          price: p.price,
          pair: p.pair
        };
        return acc;
      }, {});

      validPrices.sort((a, b) => a.price - b.price);
      const median = validPrices[Math.floor(validPrices.length / 2)].price;
      allPrices.medianPrice = median;
      
      try {
        logger.info('\nSaving results to JSON file...');
        await saveToJson(allPrices);
        logger.info('Save completed successfully');
        logger.info(`\nMedian Price: $${median.toFixed(4)}`);
        return median;
      } catch (error) {
        logger.error(`Failed to save results: ${error.message}`);
        logger.info(`\nMedian Price: $${median.toFixed(4)}`);
        return median;
      }
    }
  }
  
  logger.error('No valid prices found');
  return null;
}

// Run price check
getPrices().catch(error => {
  logger.error(`Error in main execution: ${error.message}`);
  process.exit(1);
});