{"name": "erc20-price-checker", "version": "1.0.0", "description": "A tool to fetch and monitor ERC20 token prices from various DEXes on Ethereum", "main": "price_checker.js", "scripts": {"start": "node price_checker.js", "check": "node price_checker.js --token", "monitor": "node price_checker.js --interval 60", "run-v2": "node run_price_checkers.js", "generate-checkers": "node create_token_checkers.js", "check-token": "node ./token_checkers/"}, "keywords": ["ethereum", "erc20", "dex", "price", "monitor", "uniswap", "sushiswap"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.2", "dotenv": "^16.3.1", "ethers": "^5.7.2", "winston": "^3.11.0"}}