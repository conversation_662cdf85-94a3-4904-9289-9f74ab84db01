# DEX Price Checker

A Python-based price checker for cryptocurrency trading pairs on decentralized exchanges (DEXs). Similar to CoinGecko but focused specifically on DEX data.

## Features

- Real-time price monitoring from multiple DEXs (Uniswap V2, Uniswap V3, SushiSwap)
- Price aggregation with outlier detection
- Weighted pricing based on liquidity and DEX reliability
- Support for checking multiple token prices in parallel
- JSON output for easy integration with other services
- Track prices for 15+ tokens including ETH, BTC, and major DeFi tokens (LINK, UNI, AAVE, etc.)
- Visual dashboard for price monitoring

## Setup

1. Install dependencies:
   ```bash
   npm install ethers winston dotenv
   ```

2. Configure your `.env` file with the required settings:
   ```
   # Required API Keys
   INFURA_API_KEY=your_infura_api_key_here
   
   # Token Configuration
   TOKEN_SYMBOL=LINK
   TOKEN_ADDRESS=******************************************
   TOKEN_DECIMALS=18
   
   # Output Configuration
   OUTPUT_FILE=token_prices.json
   ```

3. (Optional) Configure additional settings in the `.env` file:
   ```
   # DEX Addresses (optional - defaults will be used if not provided)
   UNISWAP_V2_ROUTER=******************************************
   UNISWAP_V3_ROUTER=******************************************
   SUSHISWAP_ROUTER=******************************************
   UNISWAP_V3_QUOTER=******************************************
   
   # Stablecoin Addresses (comma-separated lists of addresses and decimals)
   STABLECOIN_ADDRESSES=******************************************,******************************************,******************************************
   STABLECOIN_DECIMALS=6,6,18
   ```

## Supported Tokens

The price checker currently monitors the following tokens on Ethereum mainnet:

- Major cryptocurrencies: ETH, BTC, WBTC
- Stablecoins: DAI, USDC, USDT
- DeFi protocols: LINK, UNI, AAVE, SNX, YFI, CRV, COMP, MKR, MATIC

## Usage

### Check a token price using template
```bash
node token_checker_template.js
```

### Check different tokens by updating the .env file
To check different tokens, modify the token configuration in your `.env` file:

```
# Token Configuration for Ethereum
TOKEN_SYMBOL=ETH
TOKEN_ADDRESS=******************************************  # WETH
TOKEN_DECIMALS=18
```

### Creating custom token checkers
You can create custom token checkers by copying the template and configuring it:

```bash
# Create a new token checker for AAVE
cp token_checker_template.js token_checker_aave.js
```

Then update your `.env` file or create a token-specific `.env` file with the appropriate configuration.

## How It Works

1. For each token, the script queries prices from multiple DEXs in parallel
2. For each DEX, it checks multiple stablecoin pairs (USDC, USDT, DAI) to improve accuracy
3. Prices are aggregated using a weighted average, with weights based on:
   - DEX reliability (Uniswap V3 > Uniswap V2 > SushiSwap)
   - Number of successful price paths (more liquidity = higher weight)
4. Outliers beyond 2 standard deviations are filtered out when sufficient data points exist
5. Results are saved to `token_prices.json` and logged to console

## Output Example

```json
{
  "prices": {
    "ETH": {
      "sources": [
        {
          "source": "Uniswap_V2",
          "price": 3450.25,
          "paths_checked": 3,
          "timestamp": "2025-03-02T10:15:23.123456"
        },
        {
          "source": "SushiSwap",
          "price": 3445.75,
          "paths_checked": 2,
          "timestamp": "2025-03-02T10:15:23.234567"
        },
        {
          "source": "Uniswap_V3",
          "price": 3448.50,
          "paths_checked": 6,
          "timestamp": "2025-03-02T10:15:23.345678"
        }
      ],
      "price": 3448.12,
      "timestamp": "2025-03-02T10:15:23.456789"
    },
    "BTC": {
      "sources": [...],
      "price": 69420.69,
      "timestamp": "2025-03-02T10:15:24.567890"
    },
    ...
  },
  "updated_at": "2025-03-02T10:15:25.678901"
}
```

## Extending the Project

- Add more DEXs by updating the `DEX_ADDRESSES` dictionary and importing the corresponding ABIs
- Add new tokens by updating the `TOKEN_ADDRESSES` dictionary
- Implement a simple API server to serve price data
- Create a front-end dashboard to visualize prices

## License

MIT
