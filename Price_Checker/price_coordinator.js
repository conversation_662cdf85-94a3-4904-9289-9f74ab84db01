// price_coordinator.js
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const winston = require('winston');

// Configure logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ level, message, timestamp }) => {
      return `${timestamp} ${level}: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'price_coordinator.log' })
  ]
});

// Token addresses (Ethereum mainnet)
const TOKEN_ADDRESSES = {
  // Major cryptocurrencies
  'ETH': '******************************************',  // WETH
  'BTC': '******************************************',  // WBTC
  'WBTC': '******************************************',  // WBTC (explicit)
  
  // Stablecoins
  'DAI': '******************************************',  // DAI
  'USDC': '******************************************',  // USDC
  'USDT': '******************************************',  // USDT
  
  // DeFi protocol tokens
  'LINK': '******************************************',  // Chainlink
  'UNI': '******************************************',  // Uniswap
  'AAVE': '******************************************',  // Aave
  'SNX': '******************************************',  // Synthetix
  'YFI': '******************************************',  // Yearn Finance
  'CRV': '******************************************',  // Curve DAO
  'COMP': '******************************************',  // Compound
  'MKR': '******************************************',  // Maker
  'MATIC': '******************************************',  // Polygon (on Ethereum)
};

// Priority order for tokens (process important ones first)
const PRIORITY_TOKENS = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI'];
const OTHER_TOKENS = Object.keys(TOKEN_ADDRESSES).filter(
  token => !PRIORITY_TOKENS.includes(token)
);

// Run individual token price check with timeout
function checkTokenPrice(tokenSymbol, tokenAddress, timeoutMs = 30000) {
  return new Promise((resolve, reject) => {
    logger.info(`Starting price check for ${tokenSymbol}...`);
    
    // Create a temporary .env.token file with token-specific configuration
    const tokenEnvFile = path.join(__dirname, '.env.token');
    const tokenEnvContent = `
# Token Configuration
TOKEN_SYMBOL=${tokenSymbol}
TOKEN_ADDRESS=${tokenAddress}
TOKEN_DECIMALS=18 # Default, will be auto-detected
OUTPUT_FILE=token_prices.json
`;
    
    fs.writeFileSync(tokenEnvFile, tokenEnvContent, 'utf8');
    
    // Use token_checker_template.js with custom .env file
    const process = spawn('node', ['token_checker_template.js'], {
      stdio: ['ignore', 'pipe', 'pipe'],
      env: { ...process.env, DOTENV_CONFIG_PATH: tokenEnvFile }
    });
    
    let stdoutData = '';
    let stderrData = '';
    
    process.stdout.on('data', (data) => {
      stdoutData += data.toString();
    });
    
    process.stderr.on('data', (data) => {
      stderrData += data.toString();
    });
    
    // Handle process completion
    process.on('close', (code) => {
      if (code === 0) {
        logger.info(`✅ Successfully checked price for ${tokenSymbol}`);
        resolve({
          success: true,
          token: tokenSymbol
        });
      } else {
        logger.error(`❌ Error checking price for ${tokenSymbol} (exit code: ${code})`);
        logger.error(`Error output: ${stderrData}`);
        resolve({
          success: false,
          token: tokenSymbol,
          error: stderrData
        });
      }
    });
    
    // Set timeout to kill process if it takes too long
    const timeout = setTimeout(() => {
      logger.warn(`⚠️ Timeout (${timeoutMs}ms) reached for ${tokenSymbol}, killing process`);
      process.kill();
      resolve({
        success: false,
        token: tokenSymbol,
        error: 'Process timed out'
      });
    }, timeoutMs);
    
    // Clear timeout if process ends before timeout
    process.on('close', () => {
      clearTimeout(timeout);
      
      // Clean up the temporary .env.token file
      try {
        if (fs.existsSync(tokenEnvFile)) {
          fs.unlinkSync(tokenEnvFile);
        }
      } catch (error) {
        logger.warn(`Error cleaning up temporary .env file: ${error.message}`);
      }
    });
  });
}

// Process tokens concurrently in small batches
async function processTokenBatch(tokens, concurrentLimit = 3, timeoutMs = 30000) {
  const results = [];
  
  for (let i = 0; i < tokens.length; i += concurrentLimit) {
    const batchTokens = tokens.slice(i, i + concurrentLimit);
    logger.info(`Processing batch of ${batchTokens.length} tokens (${i+1}-${Math.min(i+concurrentLimit, tokens.length)} of ${tokens.length})`);
    
    const batchPromises = batchTokens.map(token => {
      const tokenAddress = TOKEN_ADDRESSES[token];
      if (!tokenAddress) {
        logger.warn(`No address found for token ${token}, skipping`);
        return Promise.resolve({ success: false, token, error: 'No address defined' });
      }
      return checkTokenPrice(token, tokenAddress, timeoutMs);
    });
    
    const batchResults = await Promise.all(batchPromises);
    
    results.push(...batchResults);
    
    // Small delay between batches to prevent system overload
    if (i + concurrentLimit < tokens.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

// Merge individual token price files into a single result file
function mergeTokenPriceFiles() {
  try {
    logger.info('Merging individual token price files...');
    
    const prices = {};
    let successCount = 0;
    
    // Read all token price files
    for (const token of [...PRIORITY_TOKENS, ...OTHER_TOKENS]) {
      const filePath = path.join(__dirname, `token_prices/${token.toLowerCase()}_price.json`);
      
      if (fs.existsSync(filePath)) {
        try {
          const fileData = fs.readFileSync(filePath, 'utf8');
          const tokenData = JSON.parse(fileData);
          
          if (tokenData && tokenData.price) {
            prices[token] = tokenData.price;
            successCount++;
          }
        } catch (error) {
          logger.error(`Error reading price file for ${token}: ${error.message}`);
        }
      }
    }
    
    // Create merged output
    const output = {
      prices: prices,
      updated_at: new Date().toISOString(),
      token_count: successCount
    };
    
    // Write to the main token_prices.json file
    fs.writeFileSync('token_prices.json', JSON.stringify(output, null, 2));
    
    logger.info(`✅ Successfully merged price data for ${successCount} tokens`);
    return successCount;
  } catch (error) {
    logger.error(`Error merging token price files: ${error.message}`);
    return 0;
  }
}

// Create token_prices and token_checkers directories if they don't exist
function ensureDirectories() {
  const dirs = ['token_prices', 'token_checkers'];
  
  for (const dir of dirs) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`Created directory: ${dir}`);
    }
  }
}

// Main function
async function main() {
  const startTime = Date.now();
  logger.info('Starting price update process...');
  
  // Ensure required directories exist
  ensureDirectories();
  
  try {
    // Process tokens in priority order
    logger.info('Processing priority tokens...');
    await processTokenBatch(PRIORITY_TOKENS, 2, 45000); // 45s timeout for important tokens
    
    logger.info('Processing remaining tokens...');
    await processTokenBatch(OTHER_TOKENS, 3, 30000); // 30s timeout for other tokens
    
    // Merge all token price files
    const successCount = mergeTokenPriceFiles();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    logger.info(`✅ Price update completed in ${duration.toFixed(2)} seconds`);
    logger.info(`Successfully retrieved prices for ${successCount} tokens`);
    
    return {
      success: true,
      tokenCount: successCount,
      duration: duration
    };
  } catch (error) {
    logger.error(`Error in price update process: ${error.message}`);
    
    // Try to merge whatever data we have
    const partialSuccessCount = mergeTokenPriceFiles();
    
    return {
      success: false,
      tokenCount: partialSuccessCount,
      error: error.message
    };
  }
}

// Run the main function
if (require.main === module) {
  main().then(result => {
    if (result.success) {
      logger.info('Price update completed successfully!');
      process.exit(0);
    } else {
      logger.error(`Price update completed with errors. Retrieved ${result.tokenCount} prices.`);
      process.exit(1);
    }
  }).catch(error => {
    logger.error(`Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  checkTokenPrice,
  mergeTokenPriceFiles,
  TOKEN_ADDRESSES
};
