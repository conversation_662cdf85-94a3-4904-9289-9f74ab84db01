// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.18;

import "@balancer-labs/v2-interfaces/contracts/vault/IVault.sol";
import "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol";

/// @dev Interface for Moonwell's mERC20 Token (Similar to Compound's cTokens)
interface IMToken {
    function mint(uint mintAmount) external returns (uint);

    function borrow(uint borrowAmount) external returns (uint);

    function repayBorrow(uint repayAmount) external returns (uint);

    function redeem(uint redeemTokens) external returns (uint);

    function borrowBalanceCurrent(address account) external returns (uint);

    function balanceOf(address owner) external view returns (uint);
}

interface IMultiRewardDistributor {
    struct RewardInfo {
        address emissionToken;
        uint totalAmount;
        uint supplySide;
        uint borrowSide;
    }

    function getOutstandingRewardsForUser(
        IMToken _mToken,
        address _user
    ) external view returns (RewardInfo[] memory);
}

/// @dev Interface for Moonwell's Comptroller (Similar to Compound)
interface IComptroller {
    function enterMarkets(
        address[] calldata
    ) external returns (uint256[] memory);

    function claimReward(address holder) external;

    function claimReward(address holder, address[] memory mTokens) external;
}

/**
 * @title LeveragedYieldFarm
 * @notice This contract implements a leveraged yield farming strategy using Moonwell and Balancer flash loans
 * @dev Uses flash loans to increase position size and yield farming returns
 */
contract LeveragedYieldFarm is IFlashLoanRecipient {
    // Events
    event Deposited(address indexed user, uint256 initialAmount, uint256 totalAmount, uint256 flashLoanAmount);
    event Withdrawn(address indexed user, uint256 initialAmount, uint256 flashLoanAmount);
    event RewardsClaimed(address indexed user, address indexed token, uint256 amount);
    event EmergencyAction(string action, uint256 timestamp);

    // State variables
    bool public paused;
    uint256 public constant MAX_TOTAL_DEPOSIT = 1000000 * 1e6; // 1M USDC
    uint256 public constant LEVERAGE_RATIO = 7; // 7x leverage
    // Contract interfaces
    IERC20 public immutable USDC;
    IMToken public immutable MOONWELL_USDC;
    IERC20 public immutable WELL;
    IComptroller public immutable comptroller;
    IMultiRewardDistributor public immutable multiRewardDistributor;
    IVault public immutable vault;

    // Contract owner
    address public immutable owner;

    struct MyFlashData {
        address flashToken;
        uint256 flashAmount;
        uint256 totalAmount;
        bool isDeposit;
    }

    modifier onlyOwner() {
        require(
            msg.sender == owner,
            "LeveragedYieldFarm: caller is not the owner!"
        );
        _;
    }

    constructor(
        address _usdcAddress,
        address _moonwellUsdcAddress,
        address _wellAddress,
        address _comptrollerAddress,
        address _multiRewardDistributorAddress,
        address _vaultAddress
    ) {
        owner = msg.sender;
        USDC = IERC20(_usdcAddress);
        MOONWELL_USDC = IMToken(_moonwellUsdcAddress);
        WELL = IERC20(_wellAddress);
        comptroller = IComptroller(_comptrollerAddress);
        multiRewardDistributor = IMultiRewardDistributor(_multiRewardDistributorAddress);
        vault = IVault(_vaultAddress);

        // Enter the mUSDC market so you can borrow another type of asset
        address[] memory mTokens = new address[](1);
        mTokens[0] = _moonwellUsdcAddress;
        uint256[] memory errors = comptroller.enterMarkets(mTokens);
        if (errors[0] != 0) {
            revert("Comptroller.enterMarkets failed.");
        }
    }

    /// @notice Don't allow contract to receive Ether by mistake
        /**
     * @dev Prevents accidental sending of Ether to the contract
     */
    fallback() external {
        revert("LeveragedYieldFarm: Ether not accepted");
    }

    /**
     * @dev Modifier to make a function callable only when the contract is not paused
     */
    modifier whenNotPaused() {
        require(!paused, "LeveragedYieldFarm: contract is paused");
        _;
    }

    /**
     * @notice Pause the contract in case of emergency
     */
    function pause() external onlyOwner {
        paused = true;
        emit EmergencyAction("contract_paused", block.timestamp);
    }

    /**
     * @notice Unpause the contract
     */
    function unpause() external onlyOwner {
        paused = false;
        emit EmergencyAction("contract_unpaused", block.timestamp);
    }

    /**
     * Deposit into the market and begin farm.
     * @param initialAmount The amount of personal USDC to be used in the farm.
     * @notice You must first send USDC to this contract before you can call this function.
     * @notice Always keep extra USDC in the contract.
     */
    /**
     * @notice Deposit into the market and begin farming with leverage
     * @param initialAmount The amount of personal USDC to be used in the farm
     * @return bool Returns true if the deposit was successful
     */
    function deposit(uint256 initialAmount) external onlyOwner whenNotPaused returns (bool) {
        require(initialAmount > 0, "LeveragedYieldFarm: Amount must be greater than 0");
        require(
            initialAmount <= MAX_TOTAL_DEPOSIT,
            "LeveragedYieldFarm: Amount exceeds maximum deposit"
        );
        // Total deposit: 30% initial amount, 70% flash loan
        uint256 totalAmount = (initialAmount * 10) / 3;

        // loan is 70% of total deposit
        uint256 flashLoanAmount = totalAmount - initialAmount;

        // Get USDC Flash Loan for "DEPOSIT"
        bool isDeposit = true;
        getFlashLoan(address(USDC), flashLoanAmount, totalAmount, isDeposit); // execution goes to `receiveFlashLoan`

        // Handle remaining execution inside handleDeposit() function

        emit Deposited(msg.sender, initialAmount, totalAmount, flashLoanAmount);
        emit Withdrawn(msg.sender, initialAmount, flashLoanAmount);
        return true;
    }

    /**
     * Withdraw from the market, and claim outstanding rewards.
     * @param initialAmount The amount the user transferred.
     * @notice Always keep extra USDC in the contract.
     */
    /**
     * @notice Withdraw from the market and claim rewards
     * @param initialAmount The initial amount that was deposited
     * @return bool Returns true if the withdrawal was successful
     */
    function withdraw(uint256 initialAmount) external onlyOwner returns (bool) {
        require(initialAmount > 0, "LeveragedYieldFarm: Amount must be greater than 0");
        uint256 currentBalance = MOONWELL_USDC.balanceOf(address(this));
        require(currentBalance > 0, "LeveragedYieldFarm: No balance to withdraw");
        // Total deposit: 30% initial amount, 70% flash loan
        uint256 totalAmount = (initialAmount * 10) / 3;

        // Loan is 70% of total deposit
        uint256 flashLoanAmount = totalAmount - initialAmount;

        // Use flash loan to payback borrowed amount
        bool isDeposit = false; //false means withdraw
        getFlashLoan(address(USDC), flashLoanAmount, totalAmount, isDeposit); // execution goes to `receiveFlashLoan`

        // Handle repayment inside handleWithdraw() function

        // Claim WELL tokens
        address[] memory mTokens = new address[](1);
        mTokens[0] = address(MOONWELL_USDC);

        comptroller.claimReward(address(this), mTokens);

        // Withdraw WELL tokens
        WELL.transfer(owner, WELL.balanceOf(address(this)));

        // Withdraw USDC to the wallet
        USDC.transfer(owner, USDC.balanceOf(address(this)));

        return true;
    }

    /**
     * Responsible for getting the flash loan.
     * @param flashToken The token being flash loaned.
     * @param flashAmount The amount to flash loan.
     * @param totalAmount The amount flash loaned + user amount transferred.
     * @param isDeposit True for depositing, false for withdrawing.
     */
    function getFlashLoan(
        address flashToken,
        uint256 flashAmount,
        uint256 totalAmount,
        bool isDeposit
    ) internal {
        // Encode MyFlashData for `receiveFlashLoan`
        bytes memory userData = abi.encode(
            MyFlashData({
                flashToken: flashToken,
                flashAmount: flashAmount,
                totalAmount: totalAmount,
                isDeposit: isDeposit
            })
        );

        // Token to flash loan, by default we are flash loaning 1 token.
        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = IERC20(flashToken);

        // Flash loan amount.
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = flashAmount;

        vault.flashLoan(this, tokens, amounts, userData); // execution goes to `receiveFlashLoan()`
    }

    /**
     * @dev This is the function that will be called postLoan
     * i.e. Encode the logic to handle your flashloaned funds here
     */
    function receiveFlashLoan(
        IERC20[] memory /* tokens */,
        uint256[] memory /* amounts */,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external {
        require(
            msg.sender == address(vault),
            "LeveragedYieldFarm: Not Balancer!"
        );

        MyFlashData memory data = abi.decode(userData, (MyFlashData));
        uint256 flashTokenBalance = IERC20(data.flashToken).balanceOf(
            address(this)
        );

        require(
            flashTokenBalance >= data.flashAmount + feeAmounts[0],
            "LeveragedYieldFarm: Not enough funds to repay Balancer loan!"
        );

        if (data.isDeposit == true) {
            handleDeposit(data.totalAmount, data.flashAmount);
        }

        if (data.isDeposit == false) {
            handleWithdraw();
        }

        IERC20(data.flashToken).transfer(
            address(vault),
            (data.flashAmount + feeAmounts[0])
        );
    }

    /**
     * Handle supplying and borrowing USDC
     * @param totalAmount The total amount of USDC to supply.
     * @param flashLoanAmount The flash amount to borrow.
     */
    function handleDeposit(
        uint256 totalAmount,
        uint256 flashLoanAmount
    ) internal returns (bool) {
        // Approve USDC tokens as collateral
        USDC.approve(address(MOONWELL_USDC), totalAmount);

        // Provide collateral by minting mUSDC tokens
        MOONWELL_USDC.mint(totalAmount);

        // Borrow USDC (to pay back the flash loan)
        MOONWELL_USDC.borrow(flashLoanAmount);

        return true;
    }

    /**
     * Handle repaying borrowed amount and
     * redeeming what was supplied.
     */
    function handleWithdraw() internal returns (bool) {
        uint256 balance;

        // Get curent borrow Balance
        balance = MOONWELL_USDC.borrowBalanceCurrent(address(this));

        // Approve tokens for repayment
        USDC.approve(address(MOONWELL_USDC), balance);

        // Repay tokens
        MOONWELL_USDC.repayBorrow(balance);

        // Get mUSDC balance
        balance = MOONWELL_USDC.balanceOf(address(this));

        // Redeem USDC
        MOONWELL_USDC.redeem(balance);

        return true;
    }

    /**
     * Withdraw any tokens accidentally sent or extra balance remaining.
     * @param _tokenAddress Token address to withdraw.
     */
    function withdrawToken(address _tokenAddress) public onlyOwner {
        uint256 balance = IERC20(_tokenAddress).balanceOf(address(this));
        IERC20(_tokenAddress).transfer(owner, balance);
    }

    /**
     * Claim any outstanding rewards.
     * @param _tokenAddress Token address to claim rewards.
     * @dev Implemented as an extra safe guard for redeeming any
     * outstanding rewards. Keep in mind rewards are automatically
     * claimed in withdraw(). You can also call getOutstandingRewards()
     * to determine if you may want to call this function.
     */
    function claimRewards(address _tokenAddress) public onlyOwner {
        address[] memory mTokens = new address[](1);
        mTokens[0] = _tokenAddress;

        comptroller.claimReward(address(this), mTokens);
    }

    /* --- PUBLIC VIEW FUNCTIONS --- */

    /**
     * @notice Get the current position details
     * @return supplied Total amount supplied to Moonwell
     * @return borrowed Total amount borrowed from Moonwell
     * @return collateralValue Current value of supplied collateral
     */
    function getPosition() public returns (
        uint256 supplied,
        uint256 borrowed,
        uint256 collateralValue
    ) {
        supplied = MOONWELL_USDC.balanceOf(address(this));
        borrowed = MOONWELL_USDC.borrowBalanceCurrent(address(this));
        collateralValue = supplied;
        return (supplied, borrowed, collateralValue);
    }

    /**
     * @notice Calculate the current health factor of the position
     * @return healthFactor The current health factor (1e18 = 100%)
     */
    function getHealthFactor() public returns (uint256 healthFactor) {
        (uint256 supplied, uint256 borrowed, ) = getPosition();
        if (borrowed == 0) return type(uint256).max;
        return (supplied * 1e18) / borrowed;
    }

    /**
     * @notice Check if the position is at risk of liquidation
     * @return bool True if the position needs attention
     */
    function isPositionAtRisk() public returns (bool) {
        uint256 healthFactor = getHealthFactor();
        return healthFactor < 125e16; // 1.25 threshold
    }


    /**
     * Check rewards for a market.
     * @param _tokenAddress Token address to check rewards for.
     */
    function getOutstandingRewards(
        address _tokenAddress
    ) public view returns (IMultiRewardDistributor.RewardInfo[] memory) {
        return
            multiRewardDistributor.getOutstandingRewardsForUser(
                IMToken(_tokenAddress),
                address(this)
            );
    }

    /**
     * @notice Emergency withdrawal function that can be called even when paused
     * @dev This will attempt to withdraw all funds and return them to the owner
     */
    function emergencyWithdraw() external onlyOwner {
        // Get current position
        uint256 borrowBalance = MOONWELL_USDC.borrowBalanceCurrent(address(this));
        uint256 mTokenBalance = MOONWELL_USDC.balanceOf(address(this));

        if (borrowBalance > 0) {
            // Repay all borrowed amounts
            USDC.approve(address(MOONWELL_USDC), borrowBalance);
            MOONWELL_USDC.repayBorrow(borrowBalance);
        }

        if (mTokenBalance > 0) {
            // Redeem all supplied collateral
            MOONWELL_USDC.redeem(mTokenBalance);
        }

        // Transfer any remaining USDC to owner
        uint256 usdcBalance = USDC.balanceOf(address(this));
        if (usdcBalance > 0) {
            USDC.transfer(owner, usdcBalance);
        }

        // Claim and transfer any WELL tokens
        address[] memory mTokens = new address[](1);
        mTokens[0] = address(MOONWELL_USDC);
        comptroller.claimReward(address(this), mTokens);
        
        uint256 wellBalance = WELL.balanceOf(address(this));
        if (wellBalance > 0) {
            WELL.transfer(owner, wellBalance);
        }

        emit EmergencyAction("emergency_withdrawal", block.timestamp);
    }
}
