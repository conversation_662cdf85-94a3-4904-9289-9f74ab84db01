const { expect } = require("chai");
const { ethers } = require("hardhat");
const { parseUnits } = require("ethers");

describe("LeveragedYieldFarm", function () {
  let leveragedYieldFarm;
  let mockUsdc;
  let mockMUsdc;
  let mockWell;
  let mockBalancerVault;
  let mockComptroller;
  let owner;
  let addr1;
  let addr2;

  const INITIAL_USDC_AMOUNT = parseUnits("10000", 6); // 10,000 USDC
  const DEPOSIT_AMOUNT = parseUnits("1000", 6); // 1,000 USDC

  beforeEach(async function () {
    [owner, addr1, addr2] = await ethers.getSigners();

    // Deploy mock contracts
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    mockUsdc = await MockERC20.deploy("USD Coin", "USDC", 6);
    await mockUsdc.waitForDeployment();

    mockWell = await MockERC20.deploy("WELL Token", "WELL", 18);
    await mockWell.waitForDeployment();

    const MockMToken = await ethers.getContractFactory("MockMToken");
    mockMUsdc = await MockMToken.deploy("Moonwell USDC", "mUSDC", await mockUsdc.getAddress());
    await mockMUsdc.waitForDeployment();

    const MockBalancerVault = await ethers.getContractFactory("MockBalancerVault");
    mockBalancerVault = await MockBalancerVault.deploy();
    await mockBalancerVault.waitForDeployment();

    const MockComptroller = await ethers.getContractFactory("MockComptroller");
    mockComptroller = await MockComptroller.deploy(await mockWell.getAddress());
    await mockComptroller.waitForDeployment();

    // Deploy LeveragedYieldFarm
    const LeveragedYieldFarm = await ethers.getContractFactory("LeveragedYieldFarm");
    leveragedYieldFarm = await LeveragedYieldFarm.deploy(
      await mockUsdc.getAddress(),
      await mockMUsdc.getAddress(),
      await mockWell.getAddress(),
      await mockComptroller.getAddress(),
      await mockMUsdc.getAddress(), // Using mUSDC address as mock reward distributor
      await mockBalancerVault.getAddress()
    );
    await leveragedYieldFarm.waitForDeployment();

    // Setup initial balances and approvals
    await mockUsdc.mint(owner.address, INITIAL_USDC_AMOUNT * 2n);
    await mockUsdc.mint(await mockBalancerVault.getAddress(), INITIAL_USDC_AMOUNT * 2n);
    
    // Approve USDC for initial deposit
    await mockUsdc.approve(await leveragedYieldFarm.getAddress(), INITIAL_USDC_AMOUNT * 2n);
    
    // Approve USDC for mUSDC minting
    await mockUsdc.approve(await mockMUsdc.getAddress(), INITIAL_USDC_AMOUNT * 2n);
    
    // Approve mUSDC for the leveraged yield farm
    await mockMUsdc.approve(await leveragedYieldFarm.getAddress(), INITIAL_USDC_AMOUNT * 2n);
  });

  // Initial basic tests
  describe("Deployment", function () {
    it("Should set the right owner", async function () {
      expect(await leveragedYieldFarm.owner()).to.equal(owner.address);
    });

    it("Should start unpaused", async function () {
      expect(await leveragedYieldFarm.paused()).to.equal(false);
    });
  });
});