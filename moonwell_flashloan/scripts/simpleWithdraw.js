const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Attempting withdrawals for:", signer.address);

    // Contract addresses
    const CONTRACT_ADDRESS = "******************************************";
    const MUSDC_ADDRESS = "******************************************";
    const USDC_ADDRESS = "******************************************";

    // Get contract instances
    const leveragedYieldFarm = await hre.ethers.getContractAt(
        "LeveragedYieldFarm",
        CONTRACT_ADDRESS,
        signer
    );
    const mUsdc = await hre.ethers.getContractAt("IMToken", MUSDC_ADDRESS);
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC_ADDRESS);

    // Check initial balances
    const initialUsdcBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
    const initialMUsdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);
    
    console.log("\nInitial Balances in Contract:");
    console.log("USDC:", hre.ethers.formatUnits(initialUsdcBalance, 6));
    console.log("mUSDC:", hre.ethers.formatUnits(initialMUsdcBalance, 8));

    // Step 1: Try to withdraw any mUSDC if available
    if (initialMUsdcBalance > 0) {
        console.log("\nAttempting to withdraw mUSDC...");
        try {
            const tx = await leveragedYieldFarm.withdraw(initialMUsdcBalance);
            console.log("Waiting for mUSDC withdrawal transaction...");
            await tx.wait();
            console.log("Successfully withdrew mUSDC!");
        } catch (error) {
            console.log("Error withdrawing mUSDC:", error.message);
        }
    } else {
        console.log("\nNo mUSDC balance to withdraw");
    }

    // Step 2: Try to withdraw USDC using withdrawToken function
    if (initialUsdcBalance > 0) {
        console.log("\nAttempting to withdraw USDC using withdrawToken function...");
        try {
            const tx = await leveragedYieldFarm.withdrawToken(USDC_ADDRESS);
            console.log("Waiting for USDC withdrawal transaction...");
            await tx.wait();
            console.log("Successfully withdrew USDC!");
        } catch (error) {
            console.log("Error withdrawing USDC:", error.message);
        }
    } else {
        console.log("\nNo USDC balance to withdraw");
    }

    // Check final balances
    const finalUsdcBalance = await usdc.balanceOf(signer.address);
    const finalMUsdcBalance = await mUsdc.balanceOf(signer.address);
    const contractFinalUsdcBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
    const contractFinalMUsdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);

    console.log("\nFinal Balances:");
    console.log("Your USDC Balance:", hre.ethers.formatUnits(finalUsdcBalance, 6));
    console.log("Your mUSDC Balance:", hre.ethers.formatUnits(finalMUsdcBalance, 8));
    console.log("\nContract Final Balances:");
    console.log("USDC:", hre.ethers.formatUnits(contractFinalUsdcBalance, 6));
    console.log("mUSDC:", hre.ethers.formatUnits(contractFinalMUsdcBalance, 8));
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("Fatal error:", error);
        process.exit(1);
    });
