const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Withdrawing with account:", signer.address);

    // Contract addresses
    const CONTRACT_ADDRESS = "******************************************";
    const MUSDC_ADDRESS = "******************************************";
    const USDC_ADDRESS = "******************************************";

    // Get contract instances
    const contract = await hre.ethers.getContractAt("LeveragedYieldFarm", CONTRACT_ADDRESS);
    const mUsdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", MUSDC_ADDRESS);
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC_ADDRESS);

    // Check initial balances
    const musdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);
    console.log("\nContract mUSDC Balance:", hre.ethers.formatUnits(musdcBalance, 6));
    
    // Let's try withdrawing a smaller amount first (e.g., 10 mUSDC)
    try {
        const withdrawAmount = hre.ethers.parseUnits("10", 6); // Start with just 10 mUSDC
        console.log(`\nAttempting to withdraw mUSDC...`);
        
        const tx = await contract.withdraw(withdrawAmount, {
            gasLimit: 1000000
        });
        
        console.log("Waiting for transaction confirmation...");
        await tx.wait();
        console.log("Withdrawal successful!");

        // Check final balances
        const finalMusdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);
        const finalUsdcBalance = await usdc.balanceOf(signer.address);
        console.log("\nFinal Balances:");
        console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(finalMusdcBalance, 6));
        console.log("Your USDC Balance:", hre.ethers.formatUnits(finalUsdcBalance, 6));
    } catch (error) {
        console.error("\nWithdrawal failed:", error.message);
        
        // Let's get more detailed error information
        console.log("\nDetailed contract state:");
        try {
            const isPaused = await contract.paused().catch(() => "N/A");
            const owner = await contract.owner().catch(() => "N/A");
            const poolBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
            
            console.log("Contract paused:", isPaused);
            console.log("Contract owner:", owner);
            console.log("Your address:", signer.address);
            console.log("Contract USDC Balance:", hre.ethers.formatUnits(poolBalance, 6));
            
            // Try to get any withdrawal limits or constraints
            try {
                const minWithdraw = await contract.minWithdrawAmount().catch(() => "N/A");
                const maxWithdraw = await contract.maxWithdrawAmount().catch(() => "N/A");
                console.log("Min Withdraw:", minWithdraw !== "N/A" ? hre.ethers.formatUnits(minWithdraw, 6) : "N/A");
                console.log("Max Withdraw:", maxWithdraw !== "N/A" ? hre.ethers.formatUnits(maxWithdraw, 6) : "N/A");
            } catch (e) {
                console.log("Couldn't fetch withdrawal limits");
            }
        } catch (e) {
            console.log("Couldn't fetch additional contract state");
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
