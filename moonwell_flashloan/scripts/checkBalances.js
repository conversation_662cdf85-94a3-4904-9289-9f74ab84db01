const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Checking balances for contract and account:", signer.address);

    // Contract addresses
    const CONTRACT_ADDRESS = "******************************************";
    const MUSDC_ADDRESS = "******************************************";
    const USDC_ADDRESS = "******************************************";
    const BALANCER_VAULT = "******************************************";

    // Get contract instances
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC_ADDRESS);
    const mUsdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", MUSDC_ADDRESS);

    // Check USDC balances
    const contractUsdcBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
    const userUsdcBalance = await usdc.balanceOf(signer.address);
    const balancerUsdcBalance = await usdc.balanceOf(BALANCER_VAULT);

    console.log("\nUSDC Balances:");
    console.log("-------------");
    console.log("Contract USDC Balance:", hre.ethers.formatUnits(contractUsdcBalance, 6));
    console.log("Your USDC Balance:", hre.ethers.formatUnits(userUsdcBalance, 6));
    console.log("Balancer Vault USDC Balance:", hre.ethers.formatUnits(balancerUsdcBalance, 6));

    // Check mUSDC balances
    const contractMUsdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);
    console.log("\nmUSDC Balances:");
    console.log("--------------");
    console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(contractMUsdcBalance, 8));

    console.log("\nNext Steps:");
    console.log("-----------");
    console.log("1. Contract needs enough USDC for the flash loan repayment during withdrawal");
    console.log("2. We might need to transfer some USDC to the contract first");
    console.log("3. Or we might need to modify the withdrawal amount");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });