const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Preparing withdrawal for:", signer.address);

    // Contract addresses
    const CONTRACT_ADDRESS = "******************************************";
    const USDC_ADDRESS = "******************************************";

    // Get contract instances
    const contract = await hre.ethers.getContractAt("LeveragedYieldFarm", CONTRACT_ADDRESS);
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC_ADDRESS);

    // Check initial balances
    const initialBalance = await usdc.balanceOf(signer.address);
    console.log("\nInitial USDC Balance:", hre.ethers.formatUnits(initialBalance, 6));

    // Calculate amount including potential fees (e.g., 0.1% fee)
    const borrowAmount = hre.ethers.parseUnits("1", 6);
    const fee = borrowAmount * BigInt(1) / BigInt(1000); // 0.1% fee
    const totalRequired = borrowAmount + fee;

    // First approve the contract to spend your USDC
    console.log("\nApproving USDC spend...");
    const approveTx = await usdc.approve(CONTRACT_ADDRESS, totalRequired);
    await approveTx.wait();

    // Check contract balance before
    const contractBalanceBefore = await usdc.balanceOf(CONTRACT_ADDRESS);
    console.log("Contract USDC Balance Before:", hre.ethers.formatUnits(contractBalanceBefore, 6));

    // Execute flash loan and repayment in one transaction
    console.log("\nExecuting flash loan...");
    try {
        const tx = await contract.executeFlashLoan(borrowAmount, {
            gasLimit: 500000  // Adjust as needed
        });
        console.log("Transaction submitted. Waiting for confirmation...");
        await tx.wait();
        console.log("Flash loan executed successfully!");
    } catch (error) {
        console.log("Error during flash loan:", error.message);
        // Log more detailed error information
        if (error.error && error.error.message) {
            console.log("Detailed error:", error.error.message);
        }
    }

    // Check final balances
    const finalBalance = await usdc.balanceOf(signer.address);
    const finalContractBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
    console.log("\nFinal Balances:");
    console.log("Your USDC Balance:", hre.ethers.formatUnits(finalBalance, 6));
    console.log("Contract USDC Balance:", hre.ethers.formatUnits(finalContractBalance, 6));
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
