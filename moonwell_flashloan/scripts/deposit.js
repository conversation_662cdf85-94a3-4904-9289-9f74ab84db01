const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Depositing with account:", signer.address);

    // Amount to deposit (1 USDC)
    const AMOUNT = hre.ethers.parseUnits("3", 3); // Start with 1 USDC for testing

    // Get your deployed contract
    const CONTRACT_ADDRESS = "******************************************";
    const contract = await hre.ethers.getContractAt("LeveragedYieldFarm", CONTRACT_ADDRESS);

    // First transfer USDC to the contract using fully qualified name
    const USDC = "******************************************";
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC);
    
    // Check USDC balance before
    const balanceBefore = await usdc.balanceOf(signer.address);
    console.log("USDC balance before:", hre.ethers.formatUnits(balanceBefore, 6));

    console.log("Transferring USDC to contract...");
    const transferTx = await usdc.transfer(CONTRACT_ADDRESS, AMOUNT);
    console.log("Waiting for transfer to be mined...");
    await transferTx.wait();
    console.log("USDC transferred to contract");

    // Now deposit
    console.log("Depositing into yield farm...");
    const tx = await contract.deposit(AMOUNT);
    console.log("Waiting for deposit to be mined...");
    await tx.wait();

    // Check USDC balance after
    const balanceAfter = await usdc.balanceOf(signer.address);
    console.log("USDC balance after:", hre.ethers.formatUnits(balanceAfter, 6));

    console.log("Successfully deposited!");
    console.log("Transaction hash:", tx.hash);
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });