const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Approving with account:", signer.address);

    // USDC contract on Optimism
    const USDC = "******************************************";
    const AMOUNT = hre.ethers.parseUnits("1000", 6); // 1000 USDC with 6 decimals

    // Your deployed contract address
    const CONTRACT_ADDRESS = "******************************************";

    // Get USDC contract instance using fully qualified name
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC);

    // Approve the contract to spend USDC
    console.log("Approving USDC...");
    const tx = await usdc.approve(CONTRACT_ADDRESS, AMOUNT);
    console.log("Waiting for transaction to be mined...");
    await tx.wait();

    console.log("Approved USDC for contract:", CONTRACT_ADDRESS);
    console.log("Transaction hash:", tx.hash);

    // Check allowance
    const allowance = await usdc.allowance(signer.address, CONTRACT_ADDRESS);
    console.log("New allowance:", hre.ethers.formatUnits(allowance, 6), "USDC");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });