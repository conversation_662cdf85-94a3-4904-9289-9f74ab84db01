const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Withdrawing with account:", signer.address);

    // Amount to withdraw (2 mUSDC)
    const AMOUNT = hre.ethers.parseUnits("2", 6);

    // Get your deployed contract
    const CONTRACT_ADDRESS = "******************************************";
    const contract = await hre.ethers.getContractAt("LeveragedYieldFarm", CONTRACT_ADDRESS);

    // Get token contracts - Note we're using mUSDC address now
    const MUSDC = "******************************************"; // mUSDC address
    const USDC = "******************************************";
    const WELL = "******************************************";
    
    const musdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", MUSDC);
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC);
    const well = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", WELL);
    
    // Check all relevant balances
    const musdcBalance = await musdc.balanceOf(signer.address);
    const contractMusdcBalance = await musdc.balanceOf(CONTRACT_ADDRESS);
    const usdcBalance = await usdc.balanceOf(signer.address);
    const wellBalance = await well.balanceOf(signer.address);
    
    console.log("\nDetailed Balance Information:");
    console.log("Your mUSDC Balance:", hre.ethers.formatUnits(musdcBalance, 6));
    console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(contractMusdcBalance, 6));
    console.log("Your USDC Balance:", hre.ethers.formatUnits(usdcBalance, 6));
    console.log("Your WELL Balance:", hre.ethers.formatUnits(wellBalance, 18));

    // Check if withdrawal amount is possible
    if (contractMusdcBalance < AMOUNT) {
        console.error(`Contract doesn't have enough mUSDC. Trying to withdraw ${hre.ethers.formatUnits(AMOUNT, 6)} but contract only has ${hre.ethers.formatUnits(contractMusdcBalance, 6)}`);
        return;
    }

    // Execute withdrawal
    console.log("\nInitiating withdrawal...");
    try {
        const tx = await contract.withdraw(AMOUNT);
        console.log("Waiting for transaction to be mined...");
        await tx.wait();
        
        // Check balances after
        const musdcAfter = await musdc.balanceOf(signer.address);
        const usdcAfter = await usdc.balanceOf(signer.address);
        const wellAfter = await well.balanceOf(signer.address);
        
        console.log("\nBalances After Withdrawal:");
        console.log("mUSDC:", hre.ethers.formatUnits(musdcAfter, 6));
        console.log("USDC:", hre.ethers.formatUnits(usdcAfter, 6));
        console.log("WELL:", hre.ethers.formatUnits(wellAfter, 18));
        
        console.log("\nTransaction hash:", tx.hash);
    } catch (error) {
        console.error("\nTransaction failed with error:", error.message);
        if (error.data) {
            console.error("Error data:", error.data);
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
