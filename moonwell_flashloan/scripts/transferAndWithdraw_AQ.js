const hre = require("hardhat");

async function main() {
    // Get the first signer (wallet) from Hardhat's ethers
    const [signer] = await hre.ethers.getSigners();
    console.log("Preparing deposit and withdrawal for:", signer.address);

    // Define the smart contract addresses we'll be interacting with
    const CONTRACT_ADDRESS = "******************************************"; // Main contract
    const USDC_ADDRESS = "******************************************";    // USDC token
    const MUSDC_ADDRESS = "******************************************";    // mUSDC token

    try {
        // Track transaction nonce to ensure proper transaction ordering
        let nonce = await signer.getNonce();
        console.log("Starting nonce:", nonce);

        // Define the standard ERC20 functions we'll need to interact with the tokens
        const ERC20_ABI = [
            "function balanceOf(address) view returns (uint256)",
            "function transfer(address to, uint256 amount) returns (bool)",
            "function approve(address spender, uint256 amount) returns (bool)",
            "function symbol() view returns (string)",
            "function decimals() view returns (uint8)",
            "function allowance(address owner, address spender) view returns (uint256)"
        ];

        // Create contract instances for both USDC and mUSDC tokens
        const usdc = new hre.ethers.Contract(USDC_ADDRESS, ERC20_ABI, signer);
        const musdc = new hre.ethers.Contract(MUSDC_ADDRESS, ERC20_ABI, signer);
        
        // Check and log initial token balances
        const initialUsdcBalance = await usdc.balanceOf(signer.address);
        const initialMusdcBalance = await musdc.balanceOf(CONTRACT_ADDRESS);
        console.log("\nInitial Balances:");
        console.log("Your USDC Balance:", hre.ethers.formatUnits(initialUsdcBalance, 6));
        console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(initialMusdcBalance, 6));

        // Check if the contract has permission to spend user's USDC
        const allowance = await usdc.allowance(signer.address, CONTRACT_ADDRESS);
        console.log("Current USDC allowance:", hre.ethers.formatUnits(allowance, 6));

        // Set the amount to deposit/withdraw (1 USDC)
        const amount = hre.ethers.parseUnits("3", 6); // Using 6 decimals for USDC

        // Approve the contract to spend USDC if the current allowance is insufficient
        if (allowance < amount) {
            console.log("\nApproving USDC spend...");
            const approveTx = await usdc.approve(CONTRACT_ADDRESS, amount, { nonce: nonce++ });
            await approveTx.wait();
            console.log("USDC approved");
        }

        // Define the main contract's ABI with deposit and withdraw functions
        const CONTRACT_ABI = [
            {
                "inputs": [{"name": "initialAmount", "type": "uint256"}],
                "name": "deposit",
                "outputs": [{"name": "", "type": "bool"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [{"name": "initialAmount", "type": "uint256"}],
                "name": "withdraw",
                "outputs": [{"name": "", "type": "bool"}],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ];

        // Create instance of the main contract
        const contract = new hre.ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

        // Step 1: Transfer USDC to the contract
        console.log("\nTransferring USDC to contract...");
        const transferTx = await usdc.transfer(CONTRACT_ADDRESS, amount, { nonce: nonce++ });
        await transferTx.wait();
        console.log("USDC transferred");

        // Step 2: Execute deposit function on the contract
        console.log("\nExecuting deposit...");
        const depositTx = await contract.deposit(amount, {
            gasLimit: 3000000,  // Set high gas limit to ensure transaction goes through
            nonce: nonce++
        });
        console.log("Deposit transaction submitted:", depositTx.hash);
        const depositReceipt = await depositTx.wait();
        
        // Check if deposit was successful
        if (depositReceipt.status === 0) {
            throw new Error("Deposit transaction failed");
        }
        console.log("Deposit completed successfully");

        // Add delay between deposit and withdrawal (10 seconds)
        console.log("\nWaiting 10 seconds before withdrawal...");
        await new Promise(resolve => setTimeout(resolve, 10000));

        // Step 3: Execute withdrawal from the contract
        console.log("\nExecuting withdrawal...");
        const withdrawTx = await contract.withdraw(amount, {
            gasLimit: 3000000,
            nonce: nonce++
        });
        
        console.log("Withdrawal transaction submitted:", withdrawTx.hash);
        const withdrawReceipt = await withdrawTx.wait();
        
        // Check if withdrawal was successful
        if (withdrawReceipt.status === 0) {
            throw new Error("Withdrawal transaction failed");
        }
        console.log("Withdrawal completed successfully");

        // Wait 5 seconds before checking final balances
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Check and log final balances
        const finalUsdcBalance = await usdc.balanceOf(signer.address);
        const finalMusdcBalance = await musdc.balanceOf(CONTRACT_ADDRESS);
        console.log("\nFinal Balances:");
        console.log("Your USDC Balance:", hre.ethers.formatUnits(finalUsdcBalance, 6));
        console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(finalMusdcBalance, 6));

        // Calculate and display profit/loss
        const profit = finalUsdcBalance.sub(initialUsdcBalance);
        console.log("\nProfit/Loss:", hre.ethers.formatUnits(profit, 6), "USDC");

        // Display detailed transaction summary
        console.log("\nTransaction Summary:");
        console.log("Initial USDC Balance:", hre.ethers.formatUnits(initialUsdcBalance, 6));
        console.log("Final USDC Balance:", hre.ethers.formatUnits(finalUsdcBalance, 6));
        if (profit.isNegative()) {
            console.log("Net Loss:", hre.ethers.formatUnits(profit.abs(), 6), "USDC");
        } else {
            console.log("Net Profit:", hre.ethers.formatUnits(profit, 6), "USDC");
        }

    } catch (error) {
        // Error handling with detailed logging
        console.log("\nError:", error.message);
        if (error.data) {
            console.log("Error data:", error.data);
        }
        if (error.transaction) {
            console.log("\nTransaction details:", error.transaction);
        }
    }
}

// Execute the main function
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
