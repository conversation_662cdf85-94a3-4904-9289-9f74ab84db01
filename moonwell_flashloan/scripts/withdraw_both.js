const hre = require("hardhat");

async function main() {
    const [signer] = await hre.ethers.getSigners();
    console.log("Withdrawing with account:", signer.address);

    // Contract addresses
    const CONTRACT_ADDRESS = "******************************************";
    const MUSDC_ADDRESS = "******************************************";
    const USDC_ADDRESS = "******************************************";

    // Get contract instances
    const contract = await hre.ethers.getContractAt("LeveragedYieldFarm", CONTRACT_ADDRESS);
    const mUsdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", MUSDC_ADDRESS);
    const usdc = await hre.ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", USDC_ADDRESS);

    // Check initial balances
    const rawMusdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);
    const rawUsdcBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
    
    const musdcBalanceNum = Number(hre.ethers.formatUnits(rawMusdcBalance, 6));
    const usdcBalanceNum = Number(hre.ethers.formatUnits(rawUsdcBalance, 6));
    
    console.log("\nInitial Balances:");
    console.log("Contract mUSDC Balance:", musdcBalanceNum);
    console.log("Contract USDC Balance:", usdcBalanceNum);

    try {
        if (musdcBalanceNum > 0) {
            // If there's mUSDC, withdraw that
            console.log("\nFound mUSDC balance, attempting to withdraw...");
            console.log("Withdrawal amount:", musdcBalanceNum, "mUSDC");
            
            const tx = await contract.withdraw(rawMusdcBalance, {
                gasLimit: 1000000
            });
            
            console.log("Waiting for transaction confirmation...");
            await tx.wait();
            console.log("mUSDC withdrawal successful!");
        } else if (usdcBalanceNum > 0) {
            // If no mUSDC but there is USDC, withdraw USDC directly
            console.log("\nNo mUSDC found, but found USDC balance. Attempting to withdraw USDC...");
            console.log("Withdrawal amount:", usdcBalanceNum, "USDC");
            
            const tx = await contract.withdrawUSDC(rawUsdcBalance, {
                gasLimit: 1000000
            });
            
            console.log("Waiting for transaction confirmation...");
            await tx.wait();
            console.log("USDC withdrawal successful!");
        } else {
            console.log("\nNo tokens to withdraw! Both mUSDC and USDC balances are 0.");
            return;
        }

        // Check final balances
        const finalMusdcBalance = await mUsdc.balanceOf(CONTRACT_ADDRESS);
        const finalUsdcBalance = await usdc.balanceOf(CONTRACT_ADDRESS);
        const userUsdcBalance = await usdc.balanceOf(signer.address);
        
        console.log("\nFinal Balances:");
        console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(finalMusdcBalance, 6));
        console.log("Contract USDC Balance:", hre.ethers.formatUnits(finalUsdcBalance, 6));
        console.log("Your USDC Balance:", hre.ethers.formatUnits(userUsdcBalance, 6));

    } catch (error) {
        console.error("\nWithdrawal failed:", error.message);
        
        // Get detailed error information
        console.log("\nDetailed contract state:");
        try {
            const isPaused = await contract.paused().catch(() => "N/A");
            const owner = await contract.owner().catch(() => "N/A");
            
            console.log("Contract paused:", isPaused);
            console.log("Contract owner:", owner);
            console.log("Your address:", signer.address);
            console.log("Contract mUSDC Balance:", hre.ethers.formatUnits(rawMusdcBalance, 6));
            console.log("Contract USDC Balance:", hre.ethers.formatUnits(rawUsdcBalance, 6));

            // Log the actual error
            console.log("\nTransaction error details:");
            console.log(error);

        } catch (e) {
            console.log("Couldn't fetch additional contract state");
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
