const hre = require("hardhat");

async function main() {
  const [deployer] = await hre.ethers.getSigners();
  console.log("Deploying contracts with the account:", deployer.address);

  const LeveragedYieldFarm = await hre.ethers.getContractFactory("LeveragedYieldFarm");
  const leveragedYieldFarm = await LeveragedYieldFarm.deploy(
    "******************************************", // USDC
    "******************************************", // mUSDC
    "******************************************", // WELL
    "******************************************", // Comptroller
    "******************************************", // Reward Distributor
    "******************************************"  // Balancer Vault
  );

  await leveragedYieldFarm.waitForDeployment();

  console.log("LeveragedYieldFarm deployed to:", await leveragedYieldFarm.getAddress());
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });