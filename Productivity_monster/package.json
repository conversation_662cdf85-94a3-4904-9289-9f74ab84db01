{"name": "productivity-monster", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.0", "expo-auth-session": "~5.0.2", "expo-web-browser": "~12.3.2", "expo-local-authentication": "~13.4.1", "expo-calendar": "~11.3.0", "expo-google-sign-in": "~11.0.0", "expo-apple-authentication": "~6.1.0", "@react-native-google-signin/google-signin": "^10.0.1", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "expo-secure-store": "~12.3.1", "@react-native-async-storage/async-storage": "1.18.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "^0.72.8", "typescript": "^5.1.3", "jest": "^29.2.1", "jest-expo": "~49.0.0", "@testing-library/react-native": "^11.5.0", "@testing-library/jest-native": "^5.4.2", "react-test-renderer": "^18.2.0"}, "private": true, "overrides": {"react": "18.2.0"}}