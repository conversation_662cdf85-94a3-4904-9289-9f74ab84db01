import express from 'express';
import { auth } from '../middleware/auth';
import { 
  createSubscription,
  cancelSubscription,
  reactivateSubscription,
  updatePaymentMethod,
  getSubscriptionStatus,
  handleWebhook
} from '../controllers/SubscriptionController';

const router = express.Router();

// Public webhook endpoint for Stripe
router.post('/webhook', express.raw({type: 'application/json'}), handleWebhook);

// Protected routes
router.use(auth);
router.get('/status', getSubscriptionStatus);
router.post('/create', createSubscription);
router.post('/cancel', cancelSubscription);
router.post('/reactivate', reactivateSubscription);
router.post('/payment-method', updatePaymentMethod);

export default router;