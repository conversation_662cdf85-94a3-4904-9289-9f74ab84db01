{"name": "productivity-monster-server", "version": "1.0.0", "description": "Backend for Productivity Monster", "main": "src/index.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "mongoose": "^7.6.3", "dotenv": "^16.3.1", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.9.0", "@types/cors": "^2.8.16", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}