import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, ScrollView } from 'react-native';
import { CardField, useStripe } from '@stripe/stripe-react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { PaymentService } from '../services/PaymentService';

const SubscribeScreen = ({ navigation }: any) => {
  const [loading, setLoading] = useState(false);
  const { createPaymentMethod } = useStripe();
  const paymentService = PaymentService.getInstance();

  const handleSubscribe = async () => {
    try {
      setLoading(true);
      
      // Create payment method with card details
      const { paymentMethod, error } = await createPaymentMethod({
        paymentMethodType: 'Card',
      });

      if (error) {
        Alert.alert('Error', error.message);
        return;
      }

      // Process subscription payment
      await paymentService.handlePayment(paymentMethod);
      
      Alert.alert(
        'Success',
        'Thank you for subscribing to Productivity Monster!',
        [{ text: 'OK', onPress: () => navigation.replace('Home') }]
      );
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Subscribe to Productivity Monster</Text>
        
        <View style={styles.planCard}>
          <Text style={styles.planTitle}>Annual Plan</Text>
          <Text style={styles.price}>$150/year</Text>
          <Text style={styles.trialText}>Start with a 3-month free trial</Text>
          
          <View style={styles.featuresList}>
            <Text style={styles.feature}>✓ Unlimited group creation</Text>
            <Text style={styles.feature}>✓ Real-time collaboration</Text>
            <Text style={styles.feature}>✓ Document sharing & editing</Text>
            <Text style={styles.feature}>✓ Advanced calendar features</Text>
            <Text style={styles.feature}>✓ Priority support</Text>
          </View>
        </View>

        <View style={styles.cardContainer}>
          <Text style={styles.cardLabel}>Card Information</Text>
          <CardField
            postalCodeEnabled={true}
            placeholder={{
              number: '4242 4242 4242 4242',
            }}
            cardStyle={styles.cardStyle}
            style={styles.cardField}
          />
        </View>

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={handleSubscribe}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Processing...' : 'Start Free Trial'}
          </Text>
        </TouchableOpacity>

        <Text style={styles.disclaimer}>
          You won't be charged during the trial period. You can cancel anytime.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  planCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  planTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  price: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  trialText: {
    color: '#28a745',
    marginBottom: 15,
  },
  featuresList: {
    marginTop: 10,
  },
  feature: {
    fontSize: 16,
    marginBottom: 8,
    color: '#666',
  },
  cardContainer: {
    marginBottom: 20,
  },
  cardLabel: {
    fontSize: 16,
    marginBottom: 10,
  },
  cardField: {
    width: '100%',
    height: 50,
    marginVertical: 10,
  },
  cardStyle: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disclaimer: {
    marginTop: 15,
    textAlign: 'center',
    color: '#666',
    fontSize: 12,
  },
});

export default SubscribeScreen;