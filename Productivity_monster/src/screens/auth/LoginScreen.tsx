  styles: StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    content: {
      flex: 1,
      padding: 20,
      justifyContent: 'center',
    },
    header: {
      alignItems: 'center',
      marginBottom: 40,
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      marginBottom: 10,
    },
    subtitle: {
      fontSize: 18,
      color: '#666',
    },
    form: {
      marginBottom: 20,
    },
    input: {
      backgroundColor: 'white',
      padding: 15,
      borderRadius: 8,
      marginBottom: 15,
      borderWidth: 1,
      borderColor: '#ddd',
    },
    button: {
      padding: 15,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 10,
    },
    primaryButton: {
      backgroundColor: '#007AFF',
    },
    biometricButton: {
      backgroundColor: '#34C759',
    },
    googleButton: {
      backgroundColor: '#DB4437',
    },
    appleButton: {
      backgroundColor: '#000000',
    },
    buttonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: 'bold',
    },
    forgotPassword: {
      alignItems: 'center',
      marginTop: 15,
    },
    forgotPasswordText: {
      color: '#007AFF',
      fontSize: 14,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: 20,
    },
    footerText: {
      color: '#666',
      fontSize: 14,
    },
    footerLink: {
      color: '#007AFF',
      fontSize: 14,
      fontWeight: 'bold',
    },
    divider: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 20,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: '#ddd',
    },
    dividerText: {
      color: '#666',
      paddingHorizontal: 10,
    },
  })