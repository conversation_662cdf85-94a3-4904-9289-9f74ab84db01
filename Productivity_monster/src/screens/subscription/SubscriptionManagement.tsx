import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { AuthService } from '../../services/AuthService';
import { PaymentService } from '../../services/PaymentService';
import { format } from 'date-fns';

const SubscriptionManagement = () => {
  const [loading, setLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState<any>(null);
  const navigation = useNavigation();
  const authService = AuthService.getInstance();
  const paymentService = PaymentService.getInstance();

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      const user = await authService.getCurrentUser();
      if (!user) {
        navigation.navigate('Login');
        return;
      }

      const status = await paymentService.getSubscriptionStatus();
      setSubscriptionData(status);
    } catch (error) {
      Alert.alert('Error', 'Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will still have access until the end of your current billing period.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await paymentService.cancelSubscription();
              await loadSubscriptionData();
              Alert.alert('Success', 'Your subscription has been canceled');
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel subscription');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleUpgradeSubscription = () => {
    navigation.navigate('SubscribeScreen');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Subscription Management</Text>

      <View style={styles.infoCard}>
        <Text style={styles.statusLabel}>Status</Text>
        <Text style={styles.statusValue}>
          {subscriptionData?.status === 'trial' ? '3-Month Trial' : 
           subscriptionData?.status === 'premium' ? 'Premium' : 'Free'}
        </Text>

        {subscriptionData?.endDate && (
          <>
            <Text style={styles.periodLabel}>
              {subscriptionData?.status === 'trial' ? 'Trial ends on:' : 'Subscription renews on:'}
            </Text>
            <Text style={styles.periodValue}>
              {format(new Date(subscriptionData.endDate), 'MMMM dd, yyyy')}
            </Text>
          </>
        )}

        {subscriptionData?.status === 'premium' && (
          <View style={styles.pricingInfo}>
            <Text style={styles.priceLabel}>Annual Plan</Text>
            <Text style={styles.priceValue}>$150/year</Text>
          </View>
        )}
      </View>

      <View style={styles.features}>
        <Text style={styles.featuresTitle}>Features Included:</Text>
        <Text style={styles.feature}>✓ Unlimited group creation</Text>
        <Text style={styles.feature}>✓ Real-time collaboration</Text>
        <Text style={styles.feature}>✓ Document sharing & editing</Text>
        <Text style={styles.feature}>✓ Advanced calendar features</Text>
        <Text style={styles.feature}>✓ Priority support</Text>
      </View>

      {subscriptionData?.status === 'none' && (
        <TouchableOpacity 
          style={[styles.button, styles.upgradeButton]} 
          onPress={handleUpgradeSubscription}
        >
          <Text style={styles.buttonText}>Upgrade to Premium</Text>
        </TouchableOpacity>
      )}

      {subscriptionData?.status === 'trial' && (
        <TouchableOpacity 
          style={[styles.button, styles.upgradeButton]} 
          onPress={handleUpgradeSubscription}
        >
          <Text style={styles.buttonText}>Convert to Premium</Text>
        </TouchableOpacity>
      )}

      {subscriptionData?.status === 'premium' && (
        <TouchableOpacity 
          style={[styles.button, styles.cancelButton]} 
          onPress={handleCancelSubscription}
        >
          <Text style={styles.cancelButtonText}>Cancel Subscription</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity 
        style={styles.supportButton}
        onPress={() => navigation.navigate('Support')}
      >
        <Text style={styles.supportButtonText}>Need help? Contact support</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusLabel: {
    fontSize: 16,
    color: '#666',
  },
  statusValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 10,
  },
  periodLabel: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  periodValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  pricingInfo: {
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  priceLabel: {
    fontSize: 16,
    color: '#666',
  },
  priceValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  features: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  feature: {
    fontSize: 16,
    marginBottom: 8,
    color: '#444',
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 10,
  },
  upgradeButton: {
    backgroundColor: '#007AFF',
  },
  cancelButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ff3b30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButtonText: {
    color: '#ff3b30',
    fontSize: 16,
    fontWeight: 'bold',
  },
  supportButton: {
    padding: 15,
    alignItems: 'center',
  },
  supportButtonText: {
    color: '#007AFF',
    fontSize: 16,
  },
});

export default SubscriptionManagement;