import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

class BiometricService {
  private static instance: BiometricService;

  private constructor() {}

  public static getInstance(): BiometricService {
    if (!BiometricService.instance) {
      BiometricService.instance = new BiometricService();
    }
    return BiometricService.instance;
  }

  async isBiometricAvailable(): Promise<boolean> {
    const compatible = await LocalAuthentication.hasHardwareAsync();
    if (!compatible) return false;

    const enrolled = await LocalAuthentication.isEnrolledAsync();
    return enrolled;
  }

  async authenticate(): Promise<boolean> {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to continue',
        fallbackLabel: 'Use passcode',
        cancelLabel: 'Cancel',
        disableDeviceFallback: false,
      });

      return result.success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  }

  async enableBiometric(userId: string): Promise<void> {
    await SecureStore.setItemAsync(`biometric_enabled_${userId}`, 'true');
  }

  async disableBiometric(userId: string): Promise<void> {
    await SecureStore.deleteItemAsync(`biometric_enabled_${userId}`);
  }

  async isBiometricEnabled(userId: string): Promise<boolean> {
    const enabled = await SecureStore.getItemAsync(`biometric_enabled_${userId}`);
    return enabled === 'true';
  }
}