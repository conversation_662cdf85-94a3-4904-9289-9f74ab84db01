import * as <PERSON> from 'expo-google-sign-in';
import * as Apple<PERSON>uthentication from 'expo-apple-authentication';
import * as Calendar from 'expo-calendar';
import { Platform } from 'react-native';
import { AuthService } from './AuthService';

interface SocialAuthResponse {
  user: {
    id: string;
    email: string;
    name: string;
    photoUrl?: string;
  };
  accessToken: string;
  provider: 'google' | 'apple';
}

class SocialAuthService {
  private static instance: SocialAuthService;
  private authService: AuthService;

  private constructor() {
    this.authService = AuthService.getInstance();
    this.initializeGoogleSignIn();
  }

  public static getInstance(): SocialAuthService {
    if (!SocialAuthService.instance) {
      SocialAuthService.instance = new SocialAuthService();
    }
    return SocialAuthService.instance;
  }

  private async initializeGoogleSignIn() {
    try {
      await Google.initAsync({
        clientId: process.env.GOOGLE_CLIENT_ID,
        scopes: [
          'profile',
          'email',
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events'
        ]
      });
    } catch (error) {
      console.error('Google Sign-In initialization error:', error);
    }
  }

  async signInWithGoogle(): Promise<SocialAuthResponse> {
    try {
      await Google.askForPlayServicesAsync();
      const { type, user } = await Google.signInAsync();
      
      if (type === 'success' && user) {
        const { accessToken } = await Google.getTokensAsync();
        if (!accessToken) throw new Error('Failed to get access token');

        // Request calendar permissions after successful sign-in
        await this.requestCalendarPermissions();

        return {
          user: {
            id: user.id || '',
            email: user.email || '',
            name: user.displayName || '',
            photoUrl: user.photoURL || undefined
          },
          accessToken,
          provider: 'google'
        };
      }
      throw new Error('Google sign-in failed');
    } catch (error) {
      console.error('Google sign-in error:', error);
      throw error;
    }
  }

  async signInWithApple(): Promise<SocialAuthResponse> {
    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      // Request calendar permissions after successful sign-in
      await this.requestCalendarPermissions();

      return {
        user: {
          id: credential.user,
          email: credential.email || '',
          name: `${credential.fullName?.givenName || ''} ${credential.fullName?.familyName || ''}`.trim(),
        },
        accessToken: credential.identityToken || '',
        provider: 'apple'
      };
    } catch (error) {
      console.error('Apple sign-in error:', error);
      throw error;
    }
  }

  private async requestCalendarPermissions(): Promise<void> {
    const { status } = await Calendar.requestCalendarPermissionsAsync();
    if (status === 'granted') {
      if (Platform.OS === 'android') {
        await Calendar.requestRemindersPermissionsAsync();
      }
    }
  }

  async syncCalendars(): Promise<void> {
    try {
      const calendars = await Calendar.getCalendarsAsync(Calendar.EntityTypes.EVENT);
      const defaultCalendars = calendars.filter(cal => 
        cal.accessLevel === Calendar.CalendarAccessLevel.OWNER && 
        (cal.source.name === 'Google' || cal.source.name === 'iCloud')
      );

      // Store calendar IDs for future sync
      for (const calendar of defaultCalendars) {
        await this.storeCalendarAccess(calendar.id);
      }
    } catch (error) {
      console.error('Calendar sync error:', error);
      throw error;
    }
  }

  private async storeCalendarAccess(calendarId: string): Promise<void> {
    // Store calendar access tokens or IDs securely
    await SecureStore.setItemAsync(`calendar_${calendarId}`, 'true');
  }

  async signOut(): Promise<void> {
    try {
      await Google.signOutAsync();
      // Clear stored calendar access
      // Additional cleanup as needed
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }
}

export default SocialAuthService;