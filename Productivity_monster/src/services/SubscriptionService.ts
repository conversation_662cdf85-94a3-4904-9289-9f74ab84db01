import { initStripe } from '@stripe/stripe-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface SubscriptionStatus {
  isActive: boolean;
  trialEnd: Date | null;
  subscriptionEnd: Date | null;
  plan: 'trial' | 'premium' | 'none';
}

class SubscriptionService {
  private static instance: SubscriptionService;

  private constructor() {
    // Initialize Stripe
    initStripe({
      publishableKey: 'your_publishable_key',
      merchantIdentifier: 'merchant.com.productivitymonster',
    });
  }

  public static getInstance(): SubscriptionService {
    if (!SubscriptionService.instance) {
      SubscriptionService.instance = new SubscriptionService();
    }
    return SubscriptionService.instance;
  }

  async startTrial(): Promise<void> {
    const trialEnd = new Date();
    trialEnd.setMonth(trialEnd.getMonth() + 3); // 3-month trial
    
    await AsyncStorage.setItem('trialStart', new Date().toISOString());
    await AsyncStorage.setItem('trialEnd', trialEnd.toISOString());
    await AsyncStorage.setItem('subscriptionStatus', 'trial');
  }

  async subscribe(): Promise<void> {
    // Implementation for $150/year subscription
    const subscriptionEnd = new Date();
    subscriptionEnd.setFullYear(subscriptionEnd.getFullYear() + 1);
    
    await AsyncStorage.setItem('subscriptionStart', new Date().toISOString());
    await AsyncStorage.setItem('subscriptionEnd', subscriptionEnd.toISOString());
    await AsyncStorage.setItem('subscriptionStatus', 'premium');
  }

  async getSubscriptionStatus(): Promise<SubscriptionStatus> {
    const status = await AsyncStorage.getItem('subscriptionStatus');
    const trialEnd = await AsyncStorage.getItem('trialEnd');
    const subscriptionEnd = await AsyncStorage.getItem('subscriptionEnd');

    return {
      isActive: status === 'trial' || status === 'premium',
      trialEnd: trialEnd ? new Date(trialEnd) : null,
      subscriptionEnd: subscriptionEnd ? new Date(subscriptionEnd) : null,
      plan: (status as 'trial' | 'premium' | 'none') || 'none'
    };
  }

  async checkSubscriptionStatus(): Promise<boolean> {
    const status = await this.getSubscriptionStatus();
    const now = new Date();

    if (status.plan === 'trial' && status.trialEnd) {
      return now < status.trialEnd;
    }

    if (status.plan === 'premium' && status.subscriptionEnd) {
      return now < status.subscriptionEnd;
    }

    return false;
  }
}