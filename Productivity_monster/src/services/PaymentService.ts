import { initStripe, createPaymentMethod, confirmPayment } from '@stripe/stripe-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from './AuthService';

interface SubscriptionStatus {
  status: 'trial' | 'premium' | 'none';
  startDate?: Date;
  endDate?: Date;
  isCanceled?: boolean;
  cancelAtPeriodEnd?: boolean;
}

class PaymentService {
  private static instance: PaymentService;
  private readonly API_URL = 'http://localhost:5000/api';
  private authService: AuthService;

  private constructor() {
    this.authService = AuthService.getInstance();
    initStripe({
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
      merchantIdentifier: 'merchant.com.productivitymonster',
    });
  }

  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  async getSubscriptionStatus(): Promise<SubscriptionStatus> {
    const token = await this.authService.getToken();
    if (!token) throw new Error('Not authenticated');

    const response = await fetch(`${this.API_URL}/subscriptions/status`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to get subscription status');
    }

    return await response.json();
  }

  async createSubscription(paymentMethodId: string): Promise<void> {
    const token = await this.authService.getToken();
    if (!token) throw new Error('Not authenticated');

    const response = await fetch(`${this.API_URL}/subscriptions/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ paymentMethodId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create subscription');
    }

    await this.updateLocalSubscriptionStatus();
  }

  async cancelSubscription(): Promise<void> {
    const token = await this.authService.getToken();
    if (!token) throw new Error('Not authenticated');

    const response = await fetch(`${this.API_URL}/subscriptions/cancel`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to cancel subscription');
    }

    await this.updateLocalSubscriptionStatus();
  }

  async reactivateSubscription(): Promise<void> {
    const token = await this.authService.getToken();
    if (!token) throw new Error('Not authenticated');

    const response = await fetch(`${this.API_URL}/subscriptions/reactivate`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to reactivate subscription');
    }

    await this.updateLocalSubscriptionStatus();
  }

  async updatePaymentMethod(paymentMethodId: string): Promise<void> {
    const token = await this.authService.getToken();
    if (!token) throw new Error('Not authenticated');

    const response = await fetch(`${this.API_URL}/subscriptions/payment-method`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ paymentMethodId }),
    });

    if (!response.ok) {
      throw new Error('Failed to update payment method');
    }
  }

  private async updateLocalSubscriptionStatus(): Promise<void> {
    const status = await this.getSubscriptionStatus();
    await AsyncStorage.setItem('subscriptionStatus', JSON.stringify(status));
  }

  async handlePayment(paymentMethodId: string): Promise<void> {
    try {
      // Create subscription with payment method
      await this.createSubscription(paymentMethodId);
      
      // Update local subscription status
      await this.updateLocalSubscriptionStatus();
    } catch (error) {
      console.error('Payment error:', error);
      throw error;
    }
  }
}

export default PaymentService;