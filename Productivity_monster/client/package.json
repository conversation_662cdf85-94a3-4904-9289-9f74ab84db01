{"name": "productivity-monster-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "socket.io-client": "^4.7.2", "@tanstack/react-query": "^5.8.4", "date-fns": "^2.30.0", "zustand": "^4.4.6"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}