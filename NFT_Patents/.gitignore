# Node modules
/node_modules
/backend/node_modules

# Compilation output
/dist

# pnpm deploy output
/bundle

# Hardhat Build Artifacts
/artifacts

# Hardhat compilation (v2) support directory
/cache

# Typechain output
/types

# Hardhat coverage reports
/coverage

# Environment variables
.env
.env.local
.env.*.local

# Logs
*.log
dev-server.log
hardhat-node.log

# Build outputs  
docs/
dist/

# Cache directories
cache/

# IDE files
.vscode/
.idea/

# Development artifacts
deployments/*undefined*

# Security reports (optional)
slither-report.json
slither-report.sarif
