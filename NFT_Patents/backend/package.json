{"name": "patent-nft-backend", "version": "1.0.0", "description": "Backend API for Patent NFT Marketplace", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-db": "node setup-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^6.14.0", "express": "^4.18.2", "multer": "^2.0.2", "pdf-lib": "^1.17.1", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.3"}, "keywords": ["patent", "nft", "stripe", "payments", "api"], "author": "Your Name", "license": "MIT"}