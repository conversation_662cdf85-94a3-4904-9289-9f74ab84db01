<!DOCTYPE html>
<html>
<head>
    <title>Debug Contract (Ethers v6)</title>
    <script type="module">
        import { ethers } from 'https://cdn.skypack.dev/ethers@6.15.0';
        
        const CONTRACT_ADDRESS = "******************************************";
        const ABI = [
            "function getMintingPrice() external view returns (uint256)",
            "function name() external view returns (string)",
            "function symbol() external view returns (string)"
        ];

        window.testContract = async function() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Testing contract with Ethers v6...</p>';

            try {
                // Check if MetaMask is available
                if (typeof window.ethereum === 'undefined') {
                    output.innerHTML += '<p>❌ MetaMask not found</p>';
                    return;
                }

                // Request account access
                await window.ethereum.request({ method: 'eth_requestAccounts' });
                
                // Create provider (ethers v6 syntax)
                const provider = new ethers.BrowserProvider(window.ethereum);
                output.innerHTML += '<p>✅ Provider created</p>';

                // Check network
                const network = await provider.getNetwork();
                output.innerHTML += `<p>🌐 Network: ${network.name} (${network.chainId})</p>`;

                // Create contract instance
                const contract = new ethers.Contract(CONTRACT_ADDRESS, ABI, provider);
                output.innerHTML += '<p>✅ Contract instance created</p>';

                // Test getMintingPrice
                output.innerHTML += '<p>📞 Calling getMintingPrice()...</p>';
                const price = await contract.getMintingPrice();
                output.innerHTML += `<p>✅ Minting price: ${ethers.formatEther(price)} ETH</p>`;

                // Test name
                const name = await contract.name();
                output.innerHTML += `<p>✅ Contract name: ${name}</p>`;

                // Test symbol
                const symbol = await contract.symbol();
                output.innerHTML += `<p>✅ Contract symbol: ${symbol}</p>`;

            } catch (error) {
                output.innerHTML += `<p>❌ Error: ${error.message}</p>`;
                output.innerHTML += `<p>❌ Error code: ${error.code}</p>`;
                output.innerHTML += `<p>❌ Error data: ${error.data}</p>`;
                console.error('Full error:', error);
            }
        }
    </script>
</head>
<body>
    <h1>Contract Debug (Ethers v6)</h1>
    <button onclick="testContract()">Test Contract</button>
    <div id="output"></div>
</body>
</html>
