import { ethers } from "hardhat";

async function main() {
  console.log("🧪 Testing PatentNFT contract...");
  
  // Get the deployed contract address
  const contractAddress = "******************************************";
  
  // Get the contract factory and attach to deployed address
  const PatentNFT = await ethers.getContractFactory("PatentNFT");
  const contract = PatentNFT.attach(contractAddress);
  
  try {
    // Test getMintingPrice function
    console.log("📞 Calling getMintingPrice()...");
    const price = await contract.getMintingPrice();
    console.log("✅ Minting price:", ethers.formatEther(price), "ETH");
    
    // Test other basic functions
    console.log("📞 Calling name()...");
    const name = await contract.name();
    console.log("✅ Contract name:", name);
    
    console.log("📞 Calling symbol()...");
    const symbol = await contract.symbol();
    console.log("✅ Contract symbol:", symbol);
    
    console.log("📞 Calling owner()...");
    const owner = await contract.owner();
    console.log("✅ Contract owner:", owner);
    
  } catch (error) {
    console.error("❌ Error testing contract:", error.message);
    console.error("Full error:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
