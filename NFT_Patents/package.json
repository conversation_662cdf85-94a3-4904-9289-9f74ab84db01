{"name": "patent-nft-marketplace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "compile": "hardhat compile", "test": "hardhat test", "test:verbose": "hardhat test --verbose", "test:gas": "REPORT_GAS=true hardhat test", "test:contracts": "hardhat test", "test:integration": "hardhat test", "test:security": "hardhat test", "security:slither": "echo 'Install Slither: pip install slither-analyzer' && echo 'Then run: slither .'", "security:solhint": "solhint 'contracts/**/*.sol'", "security:audit": "npm run security:slither && npm run security:solhint", "deploy": "node scripts/deploy-modular.js localhost", "deploy:force": "node scripts/deploy-modular.js localhost --force", "deploy:sepolia": "node scripts/deploy-modular.js sepolia", "deploy:sepolia:force": "node scripts/deploy-modular.js sepolia --force", "deploy:legacy": "hardhat run scripts/deploy-all.js --network localhost", "deploy:legacy:sepolia": "hardhat run scripts/deploy-all.js --network sepolia", "deploy:psp": "node scripts/deploy/001_deploy_psp_token.js", "deploy:search": "node scripts/deploy/002_deploy_search_payment.js", "deploy:nft": "node scripts/deploy/003_deploy_patent_nft.js", "deploy:marketplace": "node scripts/deploy/004_deploy_marketplace.js", "verify": "node scripts/verify-deployment.js", "verify:sepolia": "node scripts/verify-deployment.js sepolia", "node": "hardhat node", "deploy-pages": "npm run build && gh-pages -d docs"}, "dependencies": {"@helia/unixfs": "^5.1.0", "@openzeppelin/contracts": "^5.4.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.1", "helia": "^5.5.1", "html2canvas": "^1.4.1", "lucide-react": "^0.542.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.4.149", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.8.1"}, "devDependencies": {"@eslint/js": "^9.34.0", "@nomicfoundation/hardhat-ethers": "^4.0.0", "@nomicfoundation/hardhat-ignition": "^3.0.1", "@nomicfoundation/hardhat-mocha": "^3.0.1", "@types/node": "^22.18.0", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "chai": "^5.3.3", "dotenv": "^16.3.1", "eslint": "^9.34.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "ethers": "^6.15.0", "gh-pages": "^6.3.0", "globals": "^15.9.0", "hardhat": "^3.0.3", "mocha": "^11.7.1", "postcss": "^8.4.35", "solhint": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.41.0", "vite": "^6.1.7"}}