// SPDX-License-Identifier: MIT
pragma solidity 0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@openzeppelin/contracts/proxy/utils/Initializable.sol";

interface ILendingPool {
    function flashLoanSimple(
        address receiver,
        address asset,
        uint256 amount,
        bytes calldata params,
        uint16 referralCode
    ) external;
}

contract FlashLoanArbitrage is Ownable, Initializable {
    address public immutable LENDING_POOL;
    uint256 public gasLimitEstimate;
    uint256 public constant PROFIT_MULTIPLIER = 4;
    uint256 public minProfit;

    event ArbitrageExecuted(address token, uint256 profit, address[] routers);
    event MinProfitUpdated(uint256 newMinProfit);
    event TokensRescued(address token, uint256 amount);

    constructor(address lendingPoolAddress, address initialOwner) Ownable(initialOwner) {
        LENDING_POOL = lendingPoolAddress;
        _disableInitializers();
    }

    function initialize() external initializer {
        gasLimitEstimate = 500_000;
        minProfit = 0;
    }

    struct TradeParams {
        address router;
        address tokenIn;
        address tokenOut;
        uint24 fee;
        uint256 amountIn;
        uint256 amountOutMin;
    }

    function executeOperation(
        address borrowedToken,
        uint256 borrowedAmount,
        uint256 flashLoanFee,
        address,
        bytes calldata arbitrageParams
    ) external returns (bool) {
        require(msg.sender == LENDING_POOL, "Unauthorized caller");

        TradeParams[] memory trades = abi.decode(arbitrageParams, (TradeParams[]));
        require(trades.length >= 2, "At least two trades required");

        for (uint256 i = 0; i < trades.length; i++) {
            TradeParams memory trade = trades[i];
            IERC20(trade.tokenIn).approve(trade.router, trade.amountIn);

            uint256 amountOut = ISwapRouter(trade.router).exactInputSingle(
                ISwapRouter.ExactInputSingleParams({
                    tokenIn: trade.tokenIn,
                    tokenOut: trade.tokenOut,
                    fee: trade.fee,
                    recipient: address(this),
                    deadline: block.timestamp,
                    amountIn: trade.amountIn,
                    amountOutMinimum: trade.amountOutMin,
                    sqrtPriceLimitX96: 0
                })
            );

            if (i < trades.length - 1) {
                trades[i + 1].amountIn = amountOut;
            }
        }

        uint256 finalBalance = IERC20(borrowedToken).balanceOf(address(this));
        uint256 totalRepayment = borrowedAmount + flashLoanFee;
        require(finalBalance >= totalRepayment + minProfit, "Insufficient profit");

        IERC20(borrowedToken).approve(LENDING_POOL, totalRepayment);
        return true;
    }

    function executeArbitrage(
        address tokenAddress,
        uint256 borrowAmount,
        TradeParams[] calldata trades
    ) external onlyOwner {
        require(trades.length >= 2, "At least two trades required");
        require(trades[trades.length - 1].tokenOut == tokenAddress, "Must end with borrowed token");

        bytes memory arbitrageParams = abi.encode(trades);

        ILendingPool(LENDING_POOL).flashLoanSimple(
            address(this),
            tokenAddress,
            borrowAmount,
            arbitrageParams,
            0
        );

        uint256 profit = IERC20(tokenAddress).balanceOf(address(this));
        if (profit > 0) {
            IERC20(tokenAddress).transfer(owner(), profit);
            address[] memory routers = new address[](trades.length);
            for (uint256 i = 0; i < trades.length; i++) {
                routers[i] = trades[i].router;
            }
            emit ArbitrageExecuted(tokenAddress, profit, routers);
        }
    }

    function setMinProfit(uint256 _minProfit) external onlyOwner {
        minProfit = _minProfit;
        emit MinProfitUpdated(_minProfit);
    }

    function rescueTokens(address tokenAddress) external onlyOwner {
        uint256 balance = IERC20(tokenAddress).balanceOf(address(this));
        IERC20(tokenAddress).transfer(owner(), balance);
        emit TokensRescued(tokenAddress, balance);
    }

    function rescueETH() external onlyOwner {
        uint256 balance = address(this).balance;
        payable(owner()).transfer(balance);
    }

    receive() external payable {}
}