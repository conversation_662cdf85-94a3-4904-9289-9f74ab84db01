//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;  // Specifies the Solidity compiler version to use

import "hardhat/console.sol";  // Imports Hardhat's console for debugging
import "./Token.sol";  // Imports the Token contract

contract Crowdsale {
    // State variables that persist in contract storage
    address owner;  // Address of the contract owner/deployer
    Token public token;  // Instance of the Token contract being sold
    uint256 public price;  // Price of each token in wei
    uint256 public maxTokens;  // Maximum number of tokens available for sale
    uint256 public tokensSold;  // Running total of tokens sold
    uint256 public openingTime;  // Timestamp when the crowdsale starts

    // Events that can be emitted during contract execution
    event Buy(uint256 amount, address buyer);  // Emitted when tokens are purchased
    event Finalize(uint256 tokensSold, uint256 ethRaised);  // Emitted when sale ends

    constructor(
        Token _token,  // Address of the token contract
        uint256 _price,  // Price per token in wei
        uint256 _maxTokens,  // Maximum tokens available for sale
        uint256 _openingTime  // Timestamp when sale starts
    ) {
        // Initialize contract state variables
        owner = msg.sender;
        token = _token;
        price = _price;
        maxTokens = _maxTokens;
        openingTime = _openingTime;
    }

    // Modifier to restrict certain functions to only the owner
    modifier onlyOwner() {
        require(msg.sender == owner, "Caller is not the owner");
        _;
    }

    // Modifier to check if the crowdsale is open based on time
    modifier isCrowdsaleOpen() {
        require(block.timestamp >= openingTime, "Crowdsale is not open yet");
        _;
    }

    // Special function that allows the contract to receive ETH directly
    receive() external payable {
        uint256 amount = msg.value / price;  // Calculate tokens to buy based on ETH sent
        buyTokens(amount * 1e18);  // Convert to proper decimal places and buy tokens
    }

    // Function to purchase tokens with ETH
    function buyTokens(uint256 _amount) public payable {
        // Verify the correct amount of ETH was sent
        require(msg.value == (_amount / 1e18) * price);
        // Check if there are enough tokens available
        require(token.balanceOf(address(this)) >= _amount);
        // Transfer tokens to the buyer
        require(token.transfer(msg.sender, _amount));

        tokensSold += _amount;  // Update total tokens sold

        emit Buy(_amount, msg.sender);  // Emit purchase event
    }

    // Function to allow owner to update token price
    function setPrice(uint256 _price) public onlyOwner {
        price = _price;
    }

    // Function to end the sale and withdraw funds
    function finalize() public onlyOwner {
        // Transfer remaining tokens back to owner
        require(token.transfer(owner, token.balanceOf(address(this))));

        // Transfer all collected ETH to owner
        uint256 value = address(this).balance;
        (bool sent, ) = owner.call{value: value}("");
        require(sent);

        emit Finalize(tokensSold, value);  // Emit sale end event
    }
}
