//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import "hardhat/console.sol"; //Imports Hardhat's console for debugging
import "./Token.sol"; //Imports the Token.sol Contract

contract Crowdsale_exp {
    // State variables that persist in contract storage
    address owner; // Address of the contract owner or deployer
    Token public token; // Permission to access the Token contract and distribute the tokens from it
    uint256 public price; // Price of each token in wei
    uint256 public maxTokens; // maxTokens variable setting the max tokens available to sell
    uint256 public tokensSold; //Running total of the tokens sold
    uint public openingTime; // Timestamp when the crowdsale starts

    // Events that can be emitted during contract execution
    event Buy(uint256 amount, address buyer); // Emits the amount of tokens purchased and who the buyer was
    event Finalize(uint256 tokenSold, uint256 ethRaised); // Emits the the tokens sold and the Eth raised at the close of the sale

    constructor(
        Token _token, // Address of the token contract (could be named better)
        uint256 _price, // Price of the tokens from the token contract in wei
        uint256 _maxTokens, // Represents the max tokens avaialble in crowdsale contract
        uint256 _openingTime // Varaible representing the Unix time of when the sale opens
    ) {
        // Initializes the contract state varaiables
        owner = msg.sender;
        token = _token;
        price = _price;
        maxTokens = _maxTokens;
        openingTime = _openingTime;
    }

    // Modifier  to restrict certain functions to only the owner
    modifier onlyOwner() {
        require(msg.sender == owner, "Caller is not the owner");
        _;
    }

    // Modifier to check if the crowdsale is open based on the time
    modifier isCrowdsaleOpen() {
        require(block.timestamp >= openingTime, "Crowdsale is not open yet");
        _;
    }

    // Special function that allows the contract to recive ETH directly
    receive() external payable {
        uint256 amount = msg.value / price; // Calculate tokens to buy bases on the ETH sent to the contract
        buyTokens(amount * 1e18); // Convert to proper decimal places and buy tokens from the contract
    }

    // Function to purchase tokens with ETH
    function buyTokens(uint256 _amount) public payable { // _amount means the number of tokens the buyer wants to purchase
        // Verify the correct amount of ETH was sent
        require(msg.value == (_amount / 1e18) * price);
        // Check if there are enough tokens avaialble
        require(token.balanceOf(address(this)) >= _amount); // "address(this) means the address of this contract"
        // Transfer "_amount" of tokens from the "this" address to the buyer
        require(token.transfer(msg.sender, _amount));

        tokensSold += _amount; // Updates the total tokens sold

        emit Buy(_amount, msg.sender); // Emit purchase event
    }

    // Function to allow owner to update token price
    function setPrice(uint256 _price) public onlyOwner {
        price = _price; 
    }

    // Function to end the sale and withdraw funds
    function finalize() public onlyOwner {
        //Transfer the remaining tokens from this contract to the owners wallet
        require(token.transfer(owner, token.balanceOf(address(this))));

        //Transfer all collected ETH from buyers to the crowdsale contract, to the owners wallet
        uint256 value = address(this).balance;
        (bool sent, ) = owner.call{value: value}("");
        require(sent);

        emit Finalize(tokensSold, value); // Emits the finalized message that the crowdsale is ended to the blockchain
    }
}
