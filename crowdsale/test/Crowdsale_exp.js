const { expect } = require('chai');
const { ethers } = require('hardhat');

// Helper function to convert normal numbers to wei (ethereum's smallest unit)
// This helps us work with token amounts in a more readable way
const tokens = (n) => {
  return ethers.utils.parseUnits(n.toString(), 'ether')
}

// Alias for tokens function to make code more semantic when dealing with ETH
const ether = tokens

describe('Crowdsale', () => {
  let token, crowdsale
  let deployer, user1

  // This runs before each test, setting up the testing environment
  beforeEach(async () => {
    // Get the contract factories (blueprints) for our smart contracts
    const Crowdsale = await ethers.getContractFactory('Crowdsale')
    const Token = await ethers.getContractFactory('Token')

    // Deploy the token contract with initial parameters
    token = await Token.deploy('Dapp University', 'DAPP', '1000000')

    // Get test accounts
    accounts = await ethers.getSigners()
    deployer = accounts[0]  // Contract owner/deployer
    user1 = accounts[1]     // Test user

    // Deploy crowdsale contract with:
    // - token address (what token we're selling)
    // - price of 1 ETH per token
    // - total supply of 1,000,000 tokens
    crowdsale = await Crowdsale.deploy(token.address, ether(1), '1000000')

    // Transfer all tokens to the crowdsale contract
    let transaction = await token.connect(deployer).transfer(crowdsale.address, tokens(1000000))
    await transaction.wait()
  })

  describe('Deployment', () => {
    // Test initial setup of the crowdsale
    it('sends tokens to the Crowdsale contract', async () => {
      expect(await token.balanceOf(crowdsale.address)).to.equal(tokens(1000000))
    })

    it('returns the price', async () => {
      expect(await crowdsale.price()).to.equal(ether(1))
    })

    it('returns token address', async () => {
      expect(await crowdsale.token()).to.equal(token.address)
    })
  })

  describe('Buying Tokens', () => {
    let transaction, result
    let amount = tokens(10)

    describe('Success', () => {
      // Set up successful token purchase before each test
      beforeEach(async () => {
        // User1 buys 10 tokens for 10 ETH
        transaction = await crowdsale.connect(user1).buyTokens(amount, { value: ether(10) })
        result = await transaction.wait()
      })

      // Verify token transfer occurred correctly
      it('transfers tokens', async () => {
        expect(await token.balanceOf(crowdsale.address)).to.equal(tokens(999990))
        expect(await token.balanceOf(user1.address)).to.equal(amount)
      })

      // Check if total tokens sold is updated
      it('updates tokensSold', async () => {
        expect(await crowdsale.tokensSold()).to.equal(amount)
      })

      // Verify the Buy event was emitted with correct parameters
      it('emits a buy event', async () => {
        await expect(transaction).to.emit(crowdsale, "Buy")
          .withArgs(amount, user1.address)
      })
    })

    describe('Failure', () => {
      // Test that purchase fails if insufficient ETH is sent
      it('rejects insufficent ETH', async () => {
        await expect(crowdsale.connect(user1).buyTokens(tokens(10), { value: 0 })).to.be.reverted
      })
    })
  })
})
  // Additional tests for direct ETH sending, price updates, and sale finalization...
  // [Previous tests remain unchanged]
