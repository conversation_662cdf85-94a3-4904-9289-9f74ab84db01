const { expect } = require('chai');
const { ethers } = require('hardhat');
const { beforeEach } = require('node:test');
const { describe } = require('node:test');

// Helper function to convert normal numbers to wei
// This helps us work with token amounts in a more human readable way
const crowdsale_tokens = (n) => {
  return ethers.utils.parseUnits(n.toString(), 'ether');
};

//Alias for the 'crowdsale_tokens' function above
const ether = crowdsale_tokens;

describe('Crowdsale', () => {
  let token, crowdsale, deployer, user1;

  beforeEach(async () => {
    // Get the contract factories (blueprints) for our smart contracts
    const CrowdsaleContract = await ethers.getContractFactory('Crowdsale_dev')
    const TokenContract = await ethers.getContractFactory('Token_dev')

    // Deploy the token contract with initial parameters
    crowdsaleToken = await TokenContract.deploy('Dapp University', 'DAPP', '1000000')

    // Get test accounts from hardhat
    testAccounts = await ethers.getSigners()
    contractDeployer = testAccounts[0]  // Contract owner/deployer
    tokenBuyer = testAccounts[1]     // Test user who will buy tokens


  // Deploy the token contract with initial parameters
  // - TokenContract address is the (the tokens we're selling with this contract)
  // - price of 1 ETH per crowdsaleToken
  // - 1000000 tokens available for sale
  crowdsale = await CrowdsaleContract.deploy(crowdsaleToken.address, ether(1), '1000000')

  //Transfer all the tokens to the crowdsale contract
  let transaction = await crowdsaleToken.connect(contractDeployer).transfer(crowdsale.address, crowdsale_tokens(1000000))
  await transaction.wait()
  })

  describe('Deployment', () => {
    
  })


})