{"name": "crowdsale", "version": "1.0.0", "description": "", "dependencies": {"@openzeppelin/contracts": "v2.5", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.2.0", "ethers": "^5.7.2", "react": "^18.2.0", "react-bootstrap": "^2.4.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<EMAIL>", "license": "ISC", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "@nomicfoundation/hardhat-toolbox": "^1.0.2", "hardhat": "^2.10.1"}}