// SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import "hardhat/console.sol";

// Mapping examples (key value pairs)
contract Mappings1 {
    mapping(uint => string) public names; // Mapping of string like a databse
    mapping(uint => address) public addresses; // Mapping of string like a databse
    mapping(address => uint) public balances; // Mapping of string like a databse
    mapping(address => bool) public hasVoted; // Tracks whether a user has voted

    // Add values to the mapping in the constructor
    constructor() {
        names[1] = "Adam";
        names[2] = "<PERSON>";

        addresses[1] = ******************************************;
        addresses[2] = ******************************************;

        balances[******************************************] = 1 ether;
        balances[******************************************] = 2 ether;

        hasVoted[******************************************] = true;
        hasVoted[******************************************] = true;
    }

}

contract Mappings2 {

    // Mapping of structs
    struct Book {
        string author;
        string title;
    }

    mapping(uint => Book) public books;

    // Nested mapping
    // Track token balnces for a given account
    // E.G. Dai: ******************************************
    // E.G. WETH: ******************************************
    mapping(address => mapping(address => uint)) public balances;

    constructor() {
        books[1] = Book("A Tale of Two Cities", "Charles Dickens");
        books[2] = Book("Les Miserables", "Victor Hugo");

        address user1 = ******************************************;
        address user2 = ******************************************;
        address dai = ******************************************;
        address weth = ******************************************;

        // User 1 dai balance
        balances[user1][dai] = 1 ether;
        balances[user2][weth] = 2 ether;
    }

}

// Getting and setting values
contract Mappings3 {
    mapping(uint => string) public myMapping;

    function get(uint _id) public view returns (string memory) {
        return myMapping[_id];
    }

    function set(uint _id, string memory _value) public {
        myMapping[_id] = _value;
    }

    function remove(uint _id) public {
        // Resets the value to the default value.
        delete myMapping[_id];
    }
}


// Getting and setting nested values
contract Mappings4 {
    mapping(address => mapping(uint => bool)) public myMapping;

    function get(address _user, uint _id) public view returns (bool) {
        return myMapping[_user][_id];
    }

    function set(address _user, uint _id, bool _value) public {
       myMapping[_user][_id] = _value;
    }

    function remove(address _user, uint _id) public {
        // Resets the value to the default value.
        delete myMapping[_user][_id];
    }
}