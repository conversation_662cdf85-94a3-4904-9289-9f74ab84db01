// SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import "hardhat/console.sol";

// This contract demonstrates different ways to work with structs in Solidity
contract Structs1 {
    // Define a struct called 'Book' with three properties:
    // - title (string)
    // - author (string)
    // - completed (boolean)
    struct Book {
        string title;
        string author;
        bool completed;
    }

    // Create a dynamic array to store Book structs
    // 'public' makes it automatically getter-accessible
    Book[] public books;

    // First way to add a book: Direct struct creation in push method
    // Parameters are passed in order of struct definition
    function add1(string memory _title, string memory _author) public {
        books.push(Book(_title, _author, false));
    }

    // Second way to add a book: Named parameters
    // More explicit and safer as parameter order doesn't matter
    function add2(string memory _title, string memory _author) public {
        books.push(Book({
            title: _title,
            author: _author,
            completed: false
        }));
    }

    // Third way to add a book: Create struct in memory first
    // Shows how to initialize struct properties one by one
    function add3(string memory _title, string memory _author) public {
        Book memory book;  // Declare a temporary Book struct in memory
        book.title = _title;
        book.author = _author;
        // completed is "false" by default for boolean values

        books.push(book);
    }

    // Function to retrieve book details
    // 'view' means it doesn't modify state
    // Returns multiple values (title, author, completed status)
    function get(uint _index)
        public
        view
        returns (string memory title, string memory author, bool completed)
    {
        // 'storage' keyword means we're working with the actual stored data
        Book storage book = books[_index];
        return (book.title, book.author, book.completed);
    }

    // Function to mark a book as completed
    // Uses 'storage' to modify the actual data in the blockchain
    function complete(uint _index) public {
        Book storage book = books[_index];
        book.completed = true;
    }
}
