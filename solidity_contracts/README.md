# Solidity Variables Demo Project

This project demonstrates the fundamentals of variable usage and management in Solidity smart contracts, using Hardhat for testing and deployment.

## Project Overview

The project contains several example contracts that showcase different aspects of variable declaration, management, and usage in Solidity:

- Example 1: State variables with default values
- Example 2: Constructor-initialized state variables
- Example 3: Private variables with getter/setter functions
- Example 4: Constants and immutable variables
- Example 5: Global variables demonstration (this, msg, tx, block)

## Technologies Used

- Hardhat: Ethereum development environment
- Ethers.js: Ethereum wallet implementation and utilities
- Chai: Testing assertion library
- Solidity: Smart contract programming language

## Getting Started

### Prerequisites

- Node.js
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
```
npm install
```

