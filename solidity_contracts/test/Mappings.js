const { expect } = require('chai');
const { ethers } = require('hardhat');

const tokens = (n) => {
  return ethers.utils.parseUnits(n.toString(), 'ether')
}

const ether = tokens

describe('Mappings', () => {

  describe('Example 1', () => {

    it('demonstrates basic mappings with default values', async () => {
      const Contract = await ethers.getContractFactory('Mappings1')
      let contract = await Contract.deploy()
      expect(await contract.names(1)).to.equal('Adam')
      expect(await contract.names(2)).to.equal('Ben')
      expect(await contract.names(3)).to.equal('')
      expect(await contract.addresses(1)).to.equal('******************************************')
      expect(await contract.addresses(2)).to.equal('******************************************')
      expect(await contract.addresses(3)).to.equal('******************************************')
      expect(await contract.hasVoted('******************************************')).to.equal(true)
      expect(await contract.hasVoted('******************************************')).to.equal(true)
      expect(await contract.hasVoted('******************************************')).to.equal(false)
    })
  })

  describe('Example 2', () => {
    it('demonstrates mappings with other data types & nested mappings', async () => {
      const Contract = await ethers.getContractFactory('Mappings2')
      let contract = await Contract.deploy()

      let result =  await contract.books(1)
      expect(result[0]).to.equal('A Tale of Two Cities')
      expect(result[1]).to.equal('Charles Dickens')

      // Homework: check book 2

      let user1 = '******************************************'
      let dai = '******************************************'
      expect(await contract.balances(user1, dai)).to.equal(ether(1))

      let user2 = '******************************************'
      let weth = '******************************************'
      expect(await contract.balances(user2, weth)).to.equal(ether(2))

    })
  })

  describe('Example 3', () => {
    it('demonstrates getting and setting values', async () => {
      const Contract = await ethers.getContractFactory('Mappings3')
      let contract = await Contract.deploy()

      await contract.set(1, 'one')
      await contract.set(2, 'two')

      expect(await contract.get(1)).to.equal('one')
      expect(await contract.get(2)).to.equal('two')

      await contract.remove(1)
      expect(await contract.get(1)).to.equal('')

    })
  })

  describe('Example 4', () => {
    it('demonstrates getting and setting nested values', async () => {
      const Contract = await ethers.getContractFactory('Mappings4')
      let contract = await Contract.deploy()

      let user1 = '******************************************'
      let user2 = '******************************************'

      await contract.set(user1, 1, true)
      await contract.set(user1, 2, true)
      await contract.set(user2, 3, true)

      expect(await contract.get(user1, 1)).to.equal(true)
      expect(await contract.get(user1, 2)).to.equal(true)
      expect(await contract.get(user2, 3)).to.equal(true)

      await contract.remove(user1, 1)
      expect(await contract.get(user1, 1)).to.equal(false)

    })
  })


})
