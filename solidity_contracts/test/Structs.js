const { expect } = require('chai');
const { ethers } = require('hardhat');

describe('Structs', () => {

  describe('Example 1', () => {

    it('demonstrates read / write / update behavior of structs', async () => {
      const Contract = await ethers.getContractFactory('Structs1')
      let contract = await Contract.deploy()

      await contract.add1('A Tale of Two Cities', '<PERSON>')
      await contract.add2('Les Miserables', 'Victor Hugo')
      await contract.add3('The Hobbit', 'J.R.R<PERSON> Tolkien')

      let result = await contract.get(0);
      expect(result[0]).to.equal('A Tale of Two Cities')
      expect(result[1]).to.equal('<PERSON>')
      expect(result[2]).to.equal(false)

      // TOOD: homework - check the other books

      // Complete a book
      await contract.complete(0)
      result = await contract.get(0);
      expect(result[2]).to.equal(true)
    })
  })

})
