/* Custom styles beyond Tailwind CSS */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom focus styles */
a:focus, button:focus, input:focus, textarea:focus, select:focus {
    outline: 2px solid #2A9D8F;
    outline-offset: 2px;
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Custom animations */
.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
}

/* Custom styling for form elements */
input, textarea, select {
    transition: border-color 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    border-color: #2A9D8F;
}

/* Custom styling for buttons beyond Tailwind */
.btn-primary {
    background-color: #2A9D8F;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #264653;
}

.btn-secondary {
    background-color: #E9C46A;
    color: #264653;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #F4A261;
}

/* Custom card styling */
.custom-card {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.custom-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Image gallery styling */
.gallery-image {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.gallery-image:hover {
    transform: scale(1.05);
}

/* Weather widget styling */
.weather-widget {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Custom styling for testimonials */
.testimonial {
    position: relative;
    padding: 1.5rem;
    background-color: #f9f9f9;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.testimonial::before {
    content: '"';
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    font-size: 3rem;
    color: #E9C46A;
    opacity: 0.3;
    font-family: Georgia, serif;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Print styles */
@media print {
    header, footer, .no-print {
        display: none;
    }
    
    body {
        font-size: 12pt;
        color: #000;
        background-color: #fff;
    }
    
    a {
        text-decoration: none;
        color: #000;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
}