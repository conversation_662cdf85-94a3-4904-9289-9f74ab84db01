<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle : 'Bike Branson - E-Bike Rentals & Tours'; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Rent RAD e-bikes in Branson, Missouri and explore the beautiful Table Rock Lake trails. Park here, ride here!'; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2A9D8F',
                        secondary: '#264653',
                        accent1: '#E9C46A',
                        accent2: '#F4A261'
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    
    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
        }
        .bg-opacity-90 {
            --tw-bg-opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Header/Navigation -->
    <header class="fixed w-full z-50 bg-white shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <a href="/" class="flex items-center">
                    <span class="text-2xl font-bold text-[#264653]">Bike Branson</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-6">
                    <a href="index.php" class="text-[#264653] hover:text-[#2A9D8F] font-semibold">Home</a>
                    <a href="pages/tours.php" class="text-[#264653] hover:text-[#2A9D8F] font-semibold">Tours & Rentals</a>
                    <a href="pages/trails.php" class="text-[#264653] hover:text-[#2A9D8F] font-semibold">Trails</a>
                    <a href="pages/gallery.php" class="text-[#264653] hover:text-[#2A9D8F] font-semibold">Gallery</a>
                    <a href="pages/about.php" class="text-[#264653] hover:text-[#2A9D8F] font-semibold">About</a>
                    <a href="pages/contact.php" class="text-[#264653] hover:text-[#2A9D8F] font-semibold">Contact</a>
                    <a href="pages/book.php" class="bg-[#2A9D8F] hover:bg-[#264653] text-white py-2 px-4 rounded-lg transition duration-300">Book Now</a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-[#264653] focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="md:hidden hidden pb-4">
                <a href="index.php" class="block py-2 text-[#264653] hover:text-[#2A9D8F] font-semibold">Home</a>
                <a href="pages/tours.php" class="block py-2 text-[#264653] hover:text-[#2A9D8F] font-semibold">Tours & Rentals</a>
                <a href="pages/trails.php" class="block py-2 text-[#264653] hover:text-[#2A9D8F] font-semibold">Trails</a>
                <a href="pages/gallery.php" class="block py-2 text-[#264653] hover:text-[#2A9D8F] font-semibold">Gallery</a>
                <a href="pages/about.php" class="block py-2 text-[#264653] hover:text-[#2A9D8F] font-semibold">About</a>
                <a href="pages/contact.php" class="block py-2 text-[#264653] hover:text-[#2A9D8F] font-semibold">Contact</a>
                <a href="pages/book.php" class="block mt-4 bg-[#2A9D8F] hover:bg-[#264653] text-white py-2 px-4 rounded-lg text-center transition duration-300">Book Now</a>
            </div>
        </div>
    </header>
    
    <!-- Mobile Menu Toggle Script -->
    <script>
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
            } else {
                mobileMenu.classList.add('hidden');
            }
        });
    </script>
    
    <!-- Page Content Start -->
    <main class="pt-16"> <!-- Add padding-top to offset the fixed header -->
