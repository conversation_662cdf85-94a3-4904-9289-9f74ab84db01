<?php
    $pageTitle = "Book Your E-Bike Adventure - Bike Branson";
    $pageDescription = "Book your e-bike rental or tour with Bike Branson. Explore Table Rock Lake trails on our premium RAD e-bikes.";
    include '../includes/header.php';
?>

<!-- Hero Section -->
<section class="relative py-20 bg-gray-100">
    <div class="container mx-auto px-4 py-16">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-[#264653] mb-4">Book Your E-Bike Adventure</h1>
            <p class="text-xl text-gray-600 mb-8">Reserve your premium RAD e-bike and explore the beautiful trails of Table Rock Lake.</p>
        </div>
    </div>
</section>

<!-- Booking Form Section -->
<section class="py-16 px-4 bg-white">
    <div class="container mx-auto max-w-4xl">
        <div class="bg-gray-50 rounded-lg shadow-lg p-8">
            <form id="booking-form" action="process_booking.php" method="post" class="space-y-6">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-[#264653] mb-4">Personal Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-gray-700 font-medium mb-2">First Name</label>
                            <input type="text" id="first_name" name="first_name" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                        </div>
                        <div>
                            <label for="last_name" class="block text-gray-700 font-medium mb-2">Last Name</label>
                            <input type="text" id="last_name" name="last_name" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                        </div>
                        <div>
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email Address</label>
                            <input type="email" id="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                        </div>
                        <div>
                            <label for="phone" class="block text-gray-700 font-medium mb-2">Phone Number</label>
                            <input type="tel" id="phone" name="phone" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                        </div>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-[#264653] mb-4">Booking Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="date" class="block text-gray-700 font-medium mb-2">Date</label>
                            <input type="date" id="date" name="date" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                        </div>
                        <div>
                            <label for="time" class="block text-gray-700 font-medium mb-2">Time</label>
                            <select id="time" name="time" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                                <option value="">Select a time</option>
                                <option value="9:00 AM">9:00 AM</option>
                                <option value="10:00 AM">10:00 AM</option>
                                <option value="11:00 AM">11:00 AM</option>
                                <option value="12:00 PM">12:00 PM</option>
                                <option value="1:00 PM">1:00 PM</option>
                                <option value="2:00 PM">2:00 PM</option>
                                <option value="3:00 PM">3:00 PM</option>
                                <option value="4:00 PM">4:00 PM</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-[#264653] mb-4">E-Bike Selection</h2>
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="tour_type" class="block text-gray-700 font-medium mb-2">Tour Type</label>
                            <select id="tour_type" name="tour_type" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                                <option value="">Select a tour type</option>
                                <option value="self_guided">Self-Guided Tour</option>
                                <option value="comfort">Comfort Tour (with guide)</option>
                                <option value="family">Family Tour</option>
                                <option value="group">Group Tour (5+ people)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="num_bikes" class="block text-gray-700 font-medium mb-2">Number of Adult E-Bikes</label>
                            <select id="num_bikes" name="num_bikes" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="num_kids" class="block text-gray-700 font-medium mb-2">Number of Kid Bikes/Trailers (if applicable)</label>
                            <select id="num_kids" name="num_kids" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]">
                                <option value="0">0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="mb-6">
                    <label for="special_requests" class="block text-gray-700 font-medium mb-2">Special Requests or Notes</label>
                    <textarea id="special_requests" name="special_requests" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2A9D8F]"></textarea>
                </div>
                
                <div class="flex justify-center">
                    <button type="submit" class="bg-[#2A9D8F] hover:bg-[#264653] text-white font-bold py-3 px-8 rounded-lg transition duration-300 text-lg">Submit Booking Request</button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Pricing Information -->
<section class="py-16 px-4 bg-gray-100">
    <div class="container mx-auto max-w-5xl">
        <h2 class="text-3xl font-bold text-center text-[#264653] mb-12">Pricing Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-xl font-bold text-[#264653] mb-4">E-Bike Rentals</h3>
                <ul class="space-y-3">
                    <li class="flex justify-between">
                        <span>1 Hour Rental</span>
                        <span class="font-bold">$25/bike</span>
                    </li>
                    <li class="flex justify-between">
                        <span>2 Hour Rental</span>
                        <span class="font-bold">$40/bike</span>
                    </li>
                    <li class="flex justify-between">
                        <span>Half Day (4 hours)</span>
                        <span class="font-bold">$60/bike</span>
                    </li>
                    <li class="flex justify-between">
                        <span>Full Day (8 hours)</span>
                        <span class="font-bold">$85/bike</span>
                    </li>
                </ul>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-xl font-bold text-[#264653] mb-4">Additional Options</h3>
                <ul class="space-y-3">
                    <li class="flex justify-between">
                        <span>Kid's Bike Rental</span>
                        <span class="font-bold">$15/hour</span>
                    </li>
                    <li class="flex justify-between">
                        <span>Child Trailer</span>
                        <span class="font-bold">$20/hour</span>
                    </li>
                    <li class="flex justify-between">
                        <span>Guided Tour Add-on</span>
                        <span class="font-bold">+$25/person</span>
                    </li>
                    <li class="flex justify-between">
                        <span>Group Discount (5+ people)</span>
                        <span class="font-bold">15% off</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-xl font-bold text-[#264653] mb-4">Important Information</h3>
            <ul class="list-disc pl-5 space-y-2">
                <li>All rentals include helmets, bike locks, and trail maps.</li>
                <li>A valid ID and credit card are required for all rentals.</li>
                <li>Cancellations must be made at least 24 hours in advance for a full refund.</li>
                <li>Weather-related cancellations qualify for a full refund or rescheduling.</li>
                <li>Group discounts apply to parties of 5 or more people booking together.</li>
            </ul>
        </div>
    </div>
</section>

<?php include '../includes/footer.php'; ?>
